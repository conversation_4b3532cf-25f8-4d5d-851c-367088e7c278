<?php
/**
 * Debug Translator Access Issue
 * This script will help us identify the exact problem
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

echo "<h2>🔍 Debugging Translator Access Issue</h2>";

// Check 1: Current user info
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0073aa; margin: 10px 0;'>";
echo "<h3>1. Current User Information</h3>";
$current_user = wp_get_current_user();
echo "<p><strong>Username:</strong> " . $current_user->user_login . "</p>";
echo "<p><strong>Roles:</strong> " . implode(', ', $current_user->roles) . "</p>";
echo "<p><strong>User ID:</strong> " . $current_user->ID . "</p>";
echo "</div>";

// Check 2: Translator role existence and capabilities
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
echo "<h3>2. Translator Role Analysis</h3>";

$translator_role = get_role('translator');
if ($translator_role) {
    echo "<p style='color: green;'>✓ Translator role exists</p>";
    echo "<h4>Current Translator Capabilities:</h4>";
    echo "<ul>";
    foreach ($translator_role->capabilities as $cap => $granted) {
        $status = $granted ? '✓' : '✗';
        $color = $granted ? 'green' : 'red';
        echo "<li style='color: $color;'>$status <strong>$cap</strong></li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>✗ Translator role does NOT exist!</p>";
    echo "<h4>Available roles:</h4>";
    echo "<ul>";
    global $wp_roles;
    foreach ($wp_roles->roles as $role_name => $role_info) {
        echo "<li><strong>$role_name</strong>: " . $role_info['name'] . "</li>";
    }
    echo "</ul>";
}
echo "</div>";

// Check 3: Team system capabilities flag
echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #0073aa; margin: 10px 0;'>";
echo "<h3>3. Team System Capabilities Flag</h3>";
$capabilities_added = get_option('team_system_capabilities_added');
echo "<p><strong>team_system_capabilities_added option:</strong> ";
if ($capabilities_added) {
    echo "<span style='color: green;'>TRUE (capabilities were added before)</span>";
} else {
    echo "<span style='color: red;'>FALSE or not set (capabilities not added)</span>";
}
echo "</p>";
echo "</div>";

// Check 4: Test with a translator user
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>";
echo "<h3>4. Test Translator User Capabilities</h3>";

// Find a translator user
$translator_users = get_users(array('role' => 'translator', 'number' => 1));
if (!empty($translator_users)) {
    $test_user = $translator_users[0];
    echo "<p><strong>Test User:</strong> " . $test_user->user_login . " (ID: " . $test_user->ID . ")</p>";
    
    // Test capabilities for this user
    $test_caps = array('access_team_system', 'create_teams', 'join_teams', 'edit_posts', 'manage_options');
    echo "<h4>Capabilities Test:</h4>";
    echo "<ul>";
    foreach ($test_caps as $cap) {
        $has_cap = user_can($test_user, $cap);
        $status = $has_cap ? '✓' : '✗';
        $color = $has_cap ? 'green' : 'red';
        echo "<li style='color: $color;'>$status <strong>$cap</strong></li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>⚠ No translator users found</p>";
}
echo "</div>";

// Check 5: Menu capability logic test
echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
echo "<h3>5. Menu Capability Logic Test</h3>";

// Simulate the get_required_capability logic
echo "<h4>Testing get_required_capability() logic:</h4>";
if (!empty($translator_users)) {
    $test_user = $translator_users[0];
    echo "<p>Testing for user: <strong>" . $test_user->user_login . "</strong></p>";
    
    if (user_can($test_user, 'manage_options')) {
        $required_cap = 'manage_options';
        echo "<p>Result: <strong>manage_options</strong> (Administrator level)</p>";
    } elseif (user_can($test_user, 'access_team_system')) {
        $required_cap = 'access_team_system';
        echo "<p>Result: <strong>access_team_system</strong> (Team system access)</p>";
    } elseif (user_can($test_user, 'edit_others_posts')) {
        $required_cap = 'edit_others_posts';
        echo "<p>Result: <strong>edit_others_posts</strong> (Editor level)</p>";
    } elseif (user_can($test_user, 'publish_posts')) {
        $required_cap = 'publish_posts';
        echo "<p>Result: <strong>publish_posts</strong> (Author level)</p>";
    } else {
        $required_cap = 'edit_posts';
        echo "<p>Result: <strong>edit_posts</strong> (Contributor level)</p>";
    }
    
    echo "<p><strong>Final required capability:</strong> $required_cap</p>";
    $can_access = user_can($test_user, $required_cap);
    echo "<p><strong>Can access menu:</strong> " . ($can_access ? '<span style="color: green;">YES</span>' : '<span style="color: red;">NO</span>') . "</p>";
}
echo "</div>";

// Check 6: WordPress admin menu system
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
echo "<h3>6. WordPress Admin Menu Debug</h3>";
echo "<p>The issue might be that WordPress is not properly registering the menu for translators.</p>";
echo "<p><strong>Menu registration hook:</strong> admin_menu</p>";
echo "<p><strong>Menu callback:</strong> Team_System_Admin::display_plugin_admin_page</p>";
echo "<p><strong>Menu slug:</strong> team-system</p>";
echo "</div>";

// Action buttons
echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 20px 0;'>";
echo "<h3>🔧 Quick Fixes</h3>";

echo "<form method='post' style='display: inline-block; margin-right: 10px;'>";
echo "<input type='submit' name='reset_capabilities' value='Reset Capabilities Flag' class='button button-secondary'>";
echo "</form>";

echo "<form method='post' style='display: inline-block; margin-right: 10px;'>";
echo "<input type='submit' name='force_add_capabilities' value='Force Add Capabilities' class='button button-primary'>";
echo "</form>";

echo "<form method='post' style='display: inline-block;'>";
echo "<input type='submit' name='create_test_translator' value='Create Test Translator' class='button button-secondary'>";
echo "</form>";

echo "</div>";

// Handle form submissions
if (isset($_POST['reset_capabilities'])) {
    delete_option('team_system_capabilities_added');
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
    echo "✓ Capabilities flag reset. The add_translator_capabilities() function will run again on next page load.";
    echo "</div>";
}

if (isset($_POST['force_add_capabilities'])) {
    // Force add capabilities
    $translator_role = get_role('translator');
    if ($translator_role) {
        $translator_role->add_cap('access_team_system');
        $translator_role->add_cap('create_teams');
        $translator_role->add_cap('join_teams');
        $translator_role->add_cap('edit_posts');
        
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
        echo "✓ Capabilities forcefully added to translator role.";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "✗ Translator role not found!";
        echo "</div>";
    }
}

if (isset($_POST['create_test_translator'])) {
    $test_username = 'debug_translator_' . time();
    $user_data = array(
        'user_login' => $test_username,
        'user_pass' => 'test123',
        'user_email' => $test_username . '@test.com',
        'display_name' => 'Debug Translator',
        'role' => 'translator'
    );
    
    $user_id = wp_insert_user($user_data);
    
    if (!is_wp_error($user_id)) {
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
        echo "✓ Test translator created: <strong>$test_username</strong> / password: <strong>test123</strong>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "✗ Failed to create test user: " . $user_id->get_error_message();
        echo "</div>";
    }
}

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #0073aa; margin: 20px 0;'>";
echo "<h3>📋 Next Steps</h3>";
echo "<ol>";
echo "<li>Run the 'Reset Capabilities Flag' if the flag is TRUE</li>";
echo "<li>Run 'Force Add Capabilities' to manually add capabilities</li>";
echo "<li>Create a test translator user if none exists</li>";
echo "<li>Log in as the translator and test access to: <a href='" . admin_url('admin.php?page=team-system') . "' target='_blank'>Team System</a></li>";
echo "</ol>";
echo "</div>";

echo "<p><strong>⚠ Delete this file after debugging for security reasons.</strong></p>";
?>
