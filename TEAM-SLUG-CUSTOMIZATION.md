# تخصيص رابط الفريق (Team Slug Customization)

## الميزة الجديدة
تم إضافة إمكانية تخصيص رابط الفريق (slug) الذي يظهر في URL مثل `/teams/lazysubs/` بدلاً من إنشائه تلقائياً من اسم الفريق.

## المتطلبات
- أحرف إنجليزية صغيرة فقط (a-z)
- أرقام (0-9) 
- شرطة (-) للفصل بين الكلمات
- الحد الأدنى 3 أحرف
- يجب أن يكون فريداً (غير مستخدم من قبل فريق آخر)

## كيفية الاستخدام

### 1. عند إضافة فريق جديد
1. اذهب إلى: `/wp-admin/admin.php?page=team-system-add`
2. املأ اسم الفريق
3. في حقل "رابط الفريق":
   - اتركه فارغاً لإنشاء رابط تلقائي من اسم الفريق
   - أو أدخل رابط مخصص مثل: `lazysubs`
4. ستظهر معاينة مباشرة للرابط: `yoursite.com/teams/lazysubs`
5. سيتم التحقق من توفر الرابط تلقائياً

### 2. عند تعديل فريق موجود
1. اذهب إلى: `/wp-admin/admin.php?page=team-system&action=edit&team_id=X`
2. في تبويب "معلومات الفريق"
3. عدّل حقل "رابط الفريق"
4. احفظ التغييرات

## أمثلة على الروابط الصحيحة
```
✅ lazysubs
✅ team-alpha
✅ manga-team
✅ group123
✅ my-team-2024
```

## أمثلة على الروابط الخاطئة
```
❌ LazySubS (أحرف كبيرة)
❌ lazy_subs (شرطة سفلية)
❌ lazy subs (مسافات)
❌ فريق-الترجمة (أحرف عربية)
❌ team@123 (رموز خاصة)
❌ ab (أقل من 3 أحرف)
```

## الميزات المضافة

### 1. التحقق من التوفر في الوقت الفعلي
- عند كتابة slug مخصص، يتم التحقق من توفره فوراً
- تظهر علامة ✓ إذا كان متاحاً
- تظهر رسالة "غير متاح" إذا كان مستخدماً

### 2. الإنشاء التلقائي الذكي
- إذا تُرك الحقل فارغاً، يتم إنشاء slug من اسم الفريق
- إزالة الأحرف العربية تلقائياً
- تحويل المسافات إلى شرطات
- ضمان الفرادة بإضافة رقم إذا لزم الأمر

### 3. التحقق من الصحة
- منع إدخال أحرف غير مسموحة
- التحويل التلقائي للأحرف الصغيرة
- التحقق من الحد الأدنى للطول

## التحديثات على الملفات

### 1. admin/partials/team-system-add-team.php
- إضافة حقل team_slug
- JavaScript للتحقق من التوفر
- معاينة مباشرة للرابط
- تنسيق CSS للواجهة

### 2. admin/partials/team-system-edit-team.php  
- إضافة حقل team_slug للتعديل
- تنسيق مطابق لصفحة الإضافة

### 3. admin/class-team-system-admin.php
- دالة sanitize_team_slug()
- دالة validate_team_slug()
- دالة generate_slug_from_name()
- AJAX handler للتحقق من التوفر
- تحديث handle_add_team()
- تحديث update_team()

## كيفية عمل النظام

### عند إضافة فريق جديد:
```php
1. المستخدم يدخل اسم الفريق: "فريق الترجمة الكسول"
2. إذا لم يدخل slug مخصص:
   - يتم إنشاء slug تلقائي: "team-" + uniqid()
3. إذا أدخل slug مخصص: "lazysubs"
   - التحقق من الصحة: ✓
   - التحقق من التوفر: ✓
   - الحفظ: teams/lazysubs
```

### عند تعديل فريق:
```php
1. المستخدم يغير slug من "team-123" إلى "lazysubs"
2. التحقق من الصحة: ✓
3. التحقق من عدم استخدامه من فريق آخر: ✓
4. التحديث في قاعدة البيانات
5. الرابط الجديد: yoursite.com/teams/lazysubs
```

## استكشاف الأخطاء

### مشكلة: "رابط غير متاح"
- تأكد من عدم استخدام الرابط من فريق آخر
- جرب إضافة رقم أو كلمة: `lazysubs2` أو `lazysubs-team`

### مشكلة: "تنسيق غير صحيح"
- استخدم أحرف إنجليزية صغيرة فقط
- لا تستخدم مسافات أو رموز خاصة
- تأكد من أن الطول 3 أحرف على الأقل

### مشكلة: لا يعمل التحقق من التوفر
- تأكد من تفعيل JavaScript في المتصفح
- تحقق من console للأخطاء
- تأكد من صحة AJAX URL

## الأمان
- جميع المدخلات يتم تنظيفها بـ sanitize_team_slug()
- التحقق من nonce في AJAX requests
- منع SQL injection بـ wpdb->prepare()
- التحقق من صلاحيات المستخدم

## التوافق مع الإصدارات السابقة
- الفرق الموجودة ستحتفظ بـ slugs الحالية
- يمكن تعديل slugs الموجودة في أي وقت
- النظام يدعم كلاً من الـ slugs التلقائية والمخصصة
