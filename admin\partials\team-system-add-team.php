<div class="wrap">
    <h1>إضافة فريق جديد</h1>
    
    <div class="team-form-container">
        <form id="add-team-form" method="post" enctype="multipart/form-data">
            <?php wp_nonce_field('team_system_add_team', 'team_system_nonce'); ?>
            
            <div class="form-group">
                <label for="team_name">اسم الفريق</label>
                <input type="text" id="team_name" name="team_name" required class="regular-text">
            </div>

            <div class="form-group">
                <label for="team_slug">رابط الفريق (بأحرف إنجليزية صغيرة فقط)</label>
                <div class="slug-input-wrapper">
                    <span class="slug-prefix"><?php echo home_url('/teams/'); ?></span>
                    <input type="text" id="team_slug" name="team_slug" class="regular-text"
                           pattern="[a-z0-9\-]+"
                           title="يُسمح بالأحرف الإنجليزية الصغيرة والأرقام والشرطة فقط"
                           placeholder="مثال: lazysubs">
                </div>
                <p class="description">
                    سيكون رابط فريقك: <?php echo home_url('/teams/'); ?><span id="slug-preview">your-team-slug</span><br>
                    يُسمح بالأحرف الإنجليزية الصغيرة (a-z) والأرقام (0-9) والشرطة (-) فقط. إذا تُرك فارغاً، سيتم إنشاؤه تلقائياً من اسم الفريق.
                </p>
            </div>

            <div class="form-group">
                <label for="team_description">وصف الفريق</label>
                <?php
                wp_editor('', 'team_description', array(
                    'textarea_name' => 'team_description',
                    'media_buttons' => false,
                    'textarea_rows' => 10,
                    'teeny' => true
                ));
                ?>
            </div>
            
            <div class="form-group">
                <label for="team_logo">شعار الفريق</label>
                <input type="file" id="team_logo" name="team_logo" accept="image/*">
                <p class="description">الحجم الموصى به 200 × 200 بكسل</p>
            </div>
            
            <div class="form-group">
                <label for="team_cover">صورة الغلاف</label>
                <input type="file" id="team_cover" name="team_cover" accept="image/*">
                <p class="description">الحجم الموصى به 1200 × 400 بكسل</p>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="button button-primary">حفظ الفريق</button>
                <a href="<?php echo admin_url('admin.php?page=team-system'); ?>" class="button">إلغاء</a>
            </div>
        </form>
    </div>
</div>

<style>
.slug-input-wrapper {
    display: flex;
    align-items: center;
    max-width: 600px;
}
.slug-prefix {
    background: #f1f1f1;
    border: 1px solid #ddd;
    border-right: none;
    padding: 8px 12px;
    font-size: 14px;
    color: #666;
    white-space: nowrap;
}
#team_slug {
    border-left: none !important;
    flex: 1;
}
#slug-preview {
    font-weight: bold;
    color: #0073aa;
}
.slug-error {
    color: #d63638;
    font-weight: bold;
}
.slug-success {
    color: #00a32a;
    font-weight: bold;
}
</style>

<script>
jQuery(document).ready(function($) {
    var $teamName = $('#team_name');
    var $teamSlug = $('#team_slug');
    var $slugPreview = $('#slug-preview');
    var slugTimeout;

    // Function to generate slug from team name
    function generateSlugFromName(name) {
        return name.toLowerCase()
            .replace(/[أ-ي]/g, '') // Remove Arabic characters
            .replace(/[^a-z0-9\s\-]/g, '') // Keep only English letters, numbers, spaces, and hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
    }

    // Function to validate slug format
    function validateSlug(slug) {
        return /^[a-z0-9\-]+$/.test(slug) && slug.length >= 3;
    }

    // Function to check slug availability
    function checkSlugAvailability(slug) {
        if (!slug || slug.length < 3) {
            $slugPreview.text('your-team-slug').removeClass('slug-error slug-success');
            return;
        }

        if (!validateSlug(slug)) {
            $slugPreview.text(slug).addClass('slug-error').removeClass('slug-success');
            return;
        }

        $slugPreview.text(slug + ' (جاري التحقق...)').removeClass('slug-error slug-success');

        // Clear previous timeout
        clearTimeout(slugTimeout);

        // Check availability after 500ms delay
        slugTimeout = setTimeout(function() {
            $.post(ajaxurl, {
                action: 'check_team_slug_availability',
                slug: slug,
                nonce: '<?php echo wp_create_nonce('check_slug_nonce'); ?>'
            }, function(response) {
                if (response.success) {
                    if (response.data.available) {
                        $slugPreview.text(slug + ' ✓').addClass('slug-success').removeClass('slug-error');
                    } else {
                        $slugPreview.text(slug + ' (غير متاح)').addClass('slug-error').removeClass('slug-success');
                    }
                } else {
                    $slugPreview.text(slug).removeClass('slug-error slug-success');
                }
            });
        }, 500);
    }

    // Auto-generate slug from team name if slug field is empty
    $teamName.on('input', function() {
        if (!$teamSlug.val()) {
            var generatedSlug = generateSlugFromName($(this).val());
            checkSlugAvailability(generatedSlug);
        }
    });

    // Validate and check slug when user types in slug field
    $teamSlug.on('input', function() {
        var slug = $(this).val().toLowerCase();
        $(this).val(slug); // Force lowercase
        checkSlugAvailability(slug);
    });

    // Form validation
    $('#add-team-form').on('submit', function(e) {
        var teamName = $teamName.val().trim();
        var teamSlug = $teamSlug.val().trim();

        if (!teamName) {
            alert('يرجى إدخال اسم الفريق');
            e.preventDefault();
            return false;
        }

        if (teamSlug && !validateSlug(teamSlug)) {
            alert('رابط الفريق يجب أن يحتوي على أحرف إنجليزية صغيرة وأرقام وشرطات فقط، وأن يكون 3 أحرف على الأقل');
            e.preventDefault();
            return false;
        }

        if (teamSlug && teamSlug.length < 3) {
            alert('رابط الفريق يجب أن يكون 3 أحرف على الأقل');
            e.preventDefault();
            return false;
        }
    });
});
</script>