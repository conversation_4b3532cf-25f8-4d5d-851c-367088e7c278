<?php
/**
 * Check Theme Roles Conflict
 * This script will check if the theme's custom-roles.php is interfering with team system
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

echo "<h2>🔍 Checking Theme Roles Conflict</h2>";

// Check 1: Look for custom-roles.php in theme
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0073aa; margin: 10px 0;'>";
echo "<h3>1. Theme Custom Roles File Check</h3>";

$theme_path = get_template_directory();
$custom_roles_file = $theme_path . '/inc/custom-roles.php';

echo "<p><strong>Theme path:</strong> $theme_path</p>";
echo "<p><strong>Custom roles file:</strong> $custom_roles_file</p>";
echo "<p><strong>File exists:</strong> " . (file_exists($custom_roles_file) ? '✓ YES' : '✗ NO') . "</p>";

if (file_exists($custom_roles_file)) {
    echo "<p><strong>File is readable:</strong> " . (is_readable($custom_roles_file) ? '✓ YES' : '✗ NO') . "</p>";
    echo "<p><strong>File size:</strong> " . filesize($custom_roles_file) . " bytes</p>";
    echo "<p><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime($custom_roles_file)) . "</p>";
    
    // Try to read the file content
    if (is_readable($custom_roles_file)) {
        $content = file_get_contents($custom_roles_file);
        echo "<h4>File Content Preview (first 2000 characters):</h4>";
        echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 300px; overflow-y: auto;'>";
        echo "<pre>" . htmlspecialchars(substr($content, 0, 2000)) . "</pre>";
        if (strlen($content) > 2000) {
            echo "<p><em>... (content truncated)</em></p>";
        }
        echo "</div>";
        
        // Check if it contains translator role definition
        if (strpos($content, 'translator') !== false) {
            echo "<p style='color: orange;'><strong>⚠ File contains 'translator' references!</strong></p>";
        }
        
        // Check for role-related functions
        $role_functions = array('add_role', 'remove_role', 'get_role', 'add_cap', 'remove_cap');
        foreach ($role_functions as $func) {
            if (strpos($content, $func) !== false) {
                echo "<p style='color: orange;'>⚠ File contains <strong>$func</strong> function</p>";
            }
        }
    }
} else {
    echo "<p style='color: green;'>✓ No custom-roles.php file found in theme</p>";
}

echo "</div>";

// Check 2: Current translator role state
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
echo "<h3>2. Current Translator Role State</h3>";

$translator_role = get_role('translator');
if ($translator_role) {
    echo "<p style='color: green;'>✓ Translator role exists</p>";
    echo "<h4>Current Capabilities:</h4>";
    echo "<ul>";
    foreach ($translator_role->capabilities as $cap => $granted) {
        $status = $granted ? '✓' : '✗';
        $color = $granted ? 'green' : 'red';
        echo "<li style='color: $color;'>$status <strong>$cap</strong></li>";
    }
    echo "</ul>";
    
    // Check specifically for team system capabilities
    $team_caps = array('access_team_system', 'create_teams', 'join_teams', 'edit_posts');
    echo "<h4>Team System Capabilities Check:</h4>";
    $missing_caps = array();
    foreach ($team_caps as $cap) {
        $has_cap = isset($translator_role->capabilities[$cap]) && $translator_role->capabilities[$cap];
        $status = $has_cap ? '✓' : '✗';
        $color = $has_cap ? 'green' : 'red';
        echo "<p style='color: $color;'>$status <strong>$cap</strong></p>";
        if (!$has_cap) {
            $missing_caps[] = $cap;
        }
    }
    
    if (!empty($missing_caps)) {
        echo "<p style='color: red;'><strong>Missing capabilities:</strong> " . implode(', ', $missing_caps) . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Translator role does NOT exist!</p>";
}

echo "</div>";

// Check 3: WordPress hooks that might interfere
echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #0073aa; margin: 10px 0;'>";
echo "<h3>3. WordPress Hooks Check</h3>";

// Check for init hooks that might modify roles
global $wp_filter;

$hooks_to_check = array('init', 'wp_loaded', 'after_setup_theme', 'wp_roles_init');
foreach ($hooks_to_check as $hook) {
    if (isset($wp_filter[$hook])) {
        echo "<h4>Hook: $hook</h4>";
        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
            foreach ($callbacks as $callback) {
                $function_name = 'Unknown';
                if (is_array($callback['function'])) {
                    if (is_object($callback['function'][0])) {
                        $function_name = get_class($callback['function'][0]) . '::' . $callback['function'][1];
                    } else {
                        $function_name = $callback['function'][0] . '::' . $callback['function'][1];
                    }
                } elseif (is_string($callback['function'])) {
                    $function_name = $callback['function'];
                }
                
                echo "<p>Priority $priority: <strong>$function_name</strong></p>";
                
                // Check if it's related to roles
                if (strpos($function_name, 'role') !== false || strpos($function_name, 'cap') !== false) {
                    echo "<p style='color: orange;'>⚠ This function might affect roles/capabilities</p>";
                }
            }
        }
    }
}

echo "</div>";

// Check 4: Test role modification
echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
echo "<h3>4. Role Modification Test</h3>";

if (isset($_POST['test_add_capability'])) {
    $translator_role = get_role('translator');
    if ($translator_role) {
        $translator_role->add_cap('test_capability_' . time());
        echo "<p style='color: green;'>✓ Test capability added successfully</p>";
        
        // Check if it persisted
        $translator_role = get_role('translator'); // Refresh
        $test_caps = array_filter(array_keys($translator_role->capabilities), function($cap) {
            return strpos($cap, 'test_capability_') === 0;
        });
        
        if (!empty($test_caps)) {
            echo "<p style='color: green;'>✓ Test capability persisted: " . implode(', ', $test_caps) . "</p>";
            
            // Clean up test capabilities
            foreach ($test_caps as $test_cap) {
                $translator_role->remove_cap($test_cap);
            }
            echo "<p>✓ Test capabilities cleaned up</p>";
        } else {
            echo "<p style='color: red;'>✗ Test capability did NOT persist - something is overriding role modifications!</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Translator role not found</p>";
    }
}

echo "<form method='post'>";
echo "<input type='submit' name='test_add_capability' value='Test Role Modification' class='button button-primary'>";
echo "</form>";

echo "</div>";

// Check 5: Recommendations
echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
echo "<h3>5. Potential Solutions</h3>";

if (file_exists($custom_roles_file)) {
    echo "<h4>If theme is overriding roles:</h4>";
    echo "<ol>";
    echo "<li><strong>Modify theme's custom-roles.php</strong> to include team system capabilities</li>";
    echo "<li><strong>Add team capabilities after theme role definition</strong> using a later hook</li>";
    echo "<li><strong>Use higher priority</strong> for team system capability addition</li>";
    echo "</ol>";
    
    echo "<h4>Quick Fix Options:</h4>";
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<input type='submit' name='force_add_team_caps' value='Force Add Team Capabilities' class='button button-primary'>";
    echo "</form>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<input type='submit' name='reset_capabilities_flag' value='Reset Capabilities Flag' class='button button-secondary'>";
    echo "</form>";
}

echo "</div>";

// Handle form submissions
if (isset($_POST['force_add_team_caps'])) {
    $translator_role = get_role('translator');
    if ($translator_role) {
        $caps_to_add = array('access_team_system', 'create_teams', 'join_teams', 'edit_posts');
        foreach ($caps_to_add as $cap) {
            $translator_role->add_cap($cap);
        }
        
        // Also reset the flag so it runs again
        delete_option('team_system_capabilities_added');
        
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
        echo "✓ Team capabilities forcefully added and flag reset";
        echo "</div>";
    }
}

if (isset($_POST['reset_capabilities_flag'])) {
    delete_option('team_system_capabilities_added');
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
    echo "✓ Capabilities flag reset - team system will try to add capabilities again";
    echo "</div>";
}

echo "<p><strong>⚠ Delete this file after debugging for security reasons.</strong></p>";
?>
