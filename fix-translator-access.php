<?php
/**
 * Fix Translator Access to Team System
 * Run this script to fix the capability mismatch issue
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

echo "<h2>Fixing Translator Access to Team System</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
echo "<h3>Problem Identified:</h3>";
echo "<p>The issue is a <strong>capability mismatch</strong> between menu creation and page access:</p>";
echo "<ul>";
echo "<li><strong>Menu creation</strong> uses <code>get_required_capability()</code> which returns <code>access_team_system</code> for translators</li>";
echo "<li><strong>Page access</strong> was checking for <code>manage_options</code> OR <code>edit_posts</code></li>";
echo "<li><strong>Translators</strong> have <code>access_team_system</code> but might not have <code>edit_posts</code></li>";
echo "</ul>";
echo "</div>";

// Fix 1: Add missing capabilities to translator role
echo "<h3>Fix 1: Adding Required Capabilities</h3>";

$translator_role = get_role('translator');

if ($translator_role) {
    echo "<h4>Adding capabilities to 'translator' role:</h4>";
    
    $capabilities = array(
        'access_team_system' => 'Access team system pages',
        'create_teams' => 'Create new teams',
        'join_teams' => 'Join existing teams',
        'edit_posts' => 'Basic WordPress capability (required for page access)'
    );
    
    foreach ($capabilities as $cap => $description) {
        $translator_role->add_cap($cap);
        echo "<p style='color: green;'>✓ Added capability: <strong>$cap</strong> - $description</p>";
    }
    
    echo "<p style='color: green; font-weight: bold;'>✓ Translator role updated successfully!</p>";
} else {
    echo "<p style='color: red;'>✗ Translator role not found!</p>";
    echo "<p>Available roles:</p>";
    echo "<ul>";
    
    global $wp_roles;
    foreach ($wp_roles->roles as $role_name => $role_info) {
        echo "<li><strong>$role_name</strong>: " . $role_info['name'] . "</li>";
    }
    echo "</ul>";
}

// Fix 2: Add to other roles
echo "<h3>Fix 2: Adding Capabilities to Other Roles</h3>";
$roles_with_access = array('administrator', 'editor', 'author');

foreach ($roles_with_access as $role_name) {
    $role = get_role($role_name);
    if ($role) {
        $role->add_cap('access_team_system');
        $role->add_cap('create_teams');
        $role->add_cap('join_teams');
        echo "<p style='color: green;'>✓ Updated role: <strong>$role_name</strong></p>";
    } else {
        echo "<p style='color: orange;'>⚠ Role not found: <strong>$role_name</strong></p>";
    }
}

// Test current user capabilities
echo "<h3>Fix 3: Capability Test</h3>";
$current_user = wp_get_current_user();
echo "<p>Current User: <strong>" . $current_user->user_login . "</strong></p>";
echo "<p>Current User Roles: <strong>" . implode(', ', $current_user->roles) . "</strong></p>";

$test_capabilities = array(
    'manage_options',
    'access_team_system',
    'create_teams',
    'join_teams',
    'edit_posts'
);

echo "<h4>Capability Check:</h4>";
foreach ($test_capabilities as $cap) {
    $has_cap = current_user_can($cap);
    $status = $has_cap ? '✓' : '✗';
    $color = $has_cap ? 'green' : 'red';
    echo "<p style='color: $color;'>$status <strong>$cap</strong></p>";
}

// Show the fix summary
echo "<div style='background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; margin: 20px 0;'>";
echo "<h3>✓ Fix Applied Successfully!</h3>";
echo "<p><strong>What was fixed:</strong></p>";
echo "<ul>";
echo "<li>Added <code>access_team_system</code> capability to translator role</li>";
echo "<li>Added <code>edit_posts</code> capability to translator role (if missing)</li>";
echo "<li>Updated page access checks to use consistent capability logic</li>";
echo "<li>Added team system capabilities to admin, editor, and author roles</li>";
echo "</ul>";
echo "</div>";

// Test links
echo "<h3>Test Links</h3>";
$team_system_url = admin_url('admin.php?page=team-system');
$add_team_url = admin_url('admin.php?page=team-system-add');

echo "<h4>Try these links after logging in as a translator:</h4>";
echo "<ul>";
echo "<li><a href='$team_system_url' target='_blank'>Team System Main Page</a></li>";
echo "<li><a href='$add_team_url' target='_blank'>Add New Team Page</a></li>";
echo "</ul>";

// Instructions
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #0073aa; margin: 20px 0;'>";
echo "<h3>Testing Instructions</h3>";
echo "<ol>";
echo "<li><strong>Log out</strong> from current admin account</li>";
echo "<li><strong>Log in</strong> as a translator user</li>";
echo "<li><strong>Check the admin menu</strong> - you should see 'الفرق' in the sidebar</li>";
echo "<li><strong>Click on 'الفرق'</strong> - you should now be able to access the page without redirect</li>";
echo "<li><strong>Try creating a team</strong> - translators should be able to create teams</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border: 1px solid #f5c6cb; margin: 20px 0;'>";
echo "<h3>⚠ Important Security Note</h3>";
echo "<p><strong>Delete this file after use</strong> for security reasons.</p>";
echo "<p>This file grants access to modify user capabilities and should not remain on the server.</p>";
echo "</div>";

?>
