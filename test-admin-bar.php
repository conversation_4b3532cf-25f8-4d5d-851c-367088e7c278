<?php
/**
 * Test script for Admin Bar Notifications
 * Run this to test the admin bar notification system
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

global $wpdb;

echo "<h2>Admin Bar Notifications Test</h2>";

// Get current user info
$current_user = wp_get_current_user();
echo "<h3>Current User Info</h3>";
echo "<p>User ID: " . $current_user->ID . "</p>";
echo "<p>Username: " . $current_user->user_login . "</p>";
echo "<p>Email: " . $current_user->user_email . "</p>";

// Check for pending invitations
echo "<h3>Pending Invitations Check</h3>";
$pending_invitations = $wpdb->get_results($wpdb->prepare(
    "SELECT i.*, t.name as team_name, u.display_name as invited_by_name
     FROM {$wpdb->prefix}team_invitations i
     INNER JOIN {$wpdb->prefix}teams t ON i.team_id = t.id
     INNER JOIN {$wpdb->users} u ON i.invited_by = u.ID
     WHERE i.user_id = %d AND i.status = 'pending' AND i.expires_at > NOW()
     ORDER BY i.created_at DESC",
    $current_user->ID
));

if (!empty($pending_invitations)) {
    echo "<p style='color: green;'>✓ Found " . count($pending_invitations) . " pending invitation(s)</p>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Team</th><th>Role</th><th>Invited By</th><th>Created</th><th>Expires</th></tr>";
    
    foreach ($pending_invitations as $invitation) {
        echo "<tr>";
        echo "<td>" . esc_html($invitation->team_name) . "</td>";
        echo "<td>" . esc_html($invitation->role) . "</td>";
        echo "<td>" . esc_html($invitation->invited_by_name) . "</td>";
        echo "<td>" . date('Y-m-d H:i', strtotime($invitation->created_at)) . "</td>";
        echo "<td>" . date('Y-m-d H:i', strtotime($invitation->expires_at)) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Expected Admin Bar:</strong> Should show 'الفرق (" . count($pending_invitations) . ")' with red notification badge</p>";
} else {
    echo "<p style='color: blue;'>- No pending invitations found</p>";
    echo "<p><strong>Expected Admin Bar:</strong> Should show regular 'الفرق' link without notification</p>";
}

// Test creating a sample invitation
echo "<h3>Create Test Invitation</h3>";

// Get available users (excluding current user)
$test_users = $wpdb->get_results($wpdb->prepare(
    "SELECT ID, user_login, user_email FROM {$wpdb->users} WHERE ID != %d LIMIT 5",
    $current_user->ID
));

// Get available teams
$teams = $wpdb->get_results("SELECT id, name FROM {$wpdb->prefix}teams LIMIT 5");

if (!empty($test_users) && !empty($teams)) {
    $test_user = $test_users[0];
    $test_team = $teams[0];
    
    echo "<form method='post' style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
    echo "<h4>Create Test Invitation</h4>";
    echo "<p>This will create a test invitation that you can use to test the admin bar notification.</p>";
    
    echo "<label>Select User to Invite:</label><br>";
    echo "<select name='test_user_id'>";
    foreach ($test_users as $user) {
        echo "<option value='{$user->ID}'>{$user->user_login} ({$user->user_email})</option>";
    }
    echo "</select><br><br>";
    
    echo "<label>Select Team:</label><br>";
    echo "<select name='test_team_id'>";
    foreach ($teams as $team) {
        echo "<option value='{$team->id}'>" . esc_html($team->name) . "</option>";
    }
    echo "</select><br><br>";
    
    echo "<label>Role:</label><br>";
    echo "<select name='test_role'>";
    echo "<option value='member'>عضو</option>";
    echo "<option value='moderator'>مشرف</option>";
    echo "<option value='translator'>مترجم</option>";
    echo "</select><br><br>";
    
    echo "<input type='submit' name='create_test_invitation' value='Create Test Invitation' class='button button-primary'>";
    echo "</form>";
    
    // Process form submission
    if (isset($_POST['create_test_invitation'])) {
        $test_user_id = intval($_POST['test_user_id']);
        $test_team_id = intval($_POST['test_team_id']);
        $test_role = sanitize_text_field($_POST['test_role']);
        
        // Get user email
        $test_user_obj = get_user_by('ID', $test_user_id);
        
        $result = $wpdb->insert(
            $wpdb->prefix . 'team_invitations',
            array(
                'team_id' => $test_team_id,
                'email' => $test_user_obj->user_email,
                'user_id' => $test_user_id,
                'role' => $test_role,
                'token' => md5(uniqid() . time() . $test_user_id . $test_team_id),
                'invited_by' => $current_user->ID,
                'created_at' => current_time('mysql'),
                'expires_at' => date('Y-m-d H:i:s', strtotime('+7 days')),
                'status' => 'pending'
            )
        );
        
        if ($result) {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
            echo "✓ Test invitation created successfully!<br>";
            echo "Now log in as user '{$test_user_obj->user_login}' to see the admin bar notification.";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
            echo "✗ Failed to create test invitation: " . $wpdb->last_error;
            echo "</div>";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠ Cannot create test invitation: No users or teams available</p>";
}

// Admin Bar Test Instructions
echo "<h3>Testing Instructions</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<h4>How to Test Admin Bar Notifications:</h4>";
echo "<ol>";
echo "<li><strong>Create a test invitation</strong> using the form above</li>";
echo "<li><strong>Log out</strong> from current admin account</li>";
echo "<li><strong>Log in</strong> as the invited user</li>";
echo "<li><strong>Check the admin bar</strong> at the top - you should see 'الفرق' with a red notification badge</li>";
echo "<li><strong>Click on the notification</strong> - it should take you to the teams page</li>";
echo "<li><strong>Accept or decline</strong> the invitation</li>";
echo "<li><strong>Check the admin bar again</strong> - the notification should disappear</li>";
echo "</ol>";
echo "</div>";

// Clean up test data
echo "<h3>Clean Up Test Data</h3>";
echo "<form method='post' style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>";
echo "<h4>Remove Test Invitations</h4>";
echo "<p>This will remove all pending invitations for testing purposes.</p>";
echo "<input type='submit' name='cleanup_test_data' value='Clean Up Test Data' class='button button-secondary' onclick='return confirm(\"Are you sure you want to remove all test invitations?\");'>";
echo "</form>";

if (isset($_POST['cleanup_test_data'])) {
    $deleted = $wpdb->delete(
        $wpdb->prefix . 'team_invitations',
        array('status' => 'pending'),
        array('%s')
    );
    
    echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
    echo "✓ Cleaned up {$deleted} test invitation(s)";
    echo "</div>";
}

// Current admin bar status
echo "<h3>Current Admin Bar Status</h3>";
$current_pending = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM {$wpdb->prefix}team_invitations 
     WHERE user_id = %d AND status = 'pending' AND expires_at > NOW()",
    $current_user->ID
));

if ($current_pending > 0) {
    echo "<p style='color: green; font-weight: bold;'>Admin bar should show: الفرق ({$current_pending})</p>";
} else {
    echo "<p style='color: blue; font-weight: bold;'>Admin bar should show: الفرق (no notification)</p>";
}

echo "<h3>Quick Links</h3>";
echo "<ul>";
echo "<li><a href='" . admin_url('admin.php?page=team-system') . "' target='_blank'>Teams Management Page</a></li>";
echo "<li><a href='" . admin_url('admin.php?page=team-system-add') . "' target='_blank'>Add New Team</a></li>";
echo "<li><a href='" . admin_url('users.php') . "' target='_blank'>Users Management</a></li>";
echo "</ul>";

echo "<p><strong>Note:</strong> Delete this file after testing for security reasons.</p>";
?>
