<?php
/**
 * Debug script for Team Invitations System
 * Run this to diagnose invitation issues
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

global $wpdb;

echo "<h2>Team System Invitations Debug</h2>";

// Check if tables exist
$table_teams = $wpdb->prefix . 'teams';
$table_invitations = $wpdb->prefix . 'team_invitations';
$table_members = $wpdb->prefix . 'team_members';

echo "<h3>1. Table Structure Check</h3>";

// Check teams table
if ($wpdb->get_var("SHOW TABLES LIKE '$table_teams'") == $table_teams) {
    echo "<p style='color: green;'>✓ Teams table exists: $table_teams</p>";
} else {
    echo "<p style='color: red;'>✗ Teams table missing: $table_teams</p>";
}

// Check invitations table
if ($wpdb->get_var("SHOW TABLES LIKE '$table_invitations'") == $table_invitations) {
    echo "<p style='color: green;'>✓ Invitations table exists: $table_invitations</p>";
    
    // Show table structure
    $columns = $wpdb->get_results("DESCRIBE $table_invitations");
    echo "<h4>Invitations table structure:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$column->Default}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>✗ Invitations table missing: $table_invitations</p>";
    echo "<p><strong>Solution:</strong> Run the update-invitations-table.php script again.</p>";
}

// Check members table
if ($wpdb->get_var("SHOW TABLES LIKE '$table_members'") == $table_members) {
    echo "<p style='color: green;'>✓ Members table exists: $table_members</p>";
} else {
    echo "<p style='color: red;'>✗ Members table missing: $table_members</p>";
}

echo "<h3>2. Test Data Insertion</h3>";

if ($wpdb->get_var("SHOW TABLES LIKE '$table_invitations'") == $table_invitations) {
    // Try to insert a test invitation
    $test_data = array(
        'team_id' => 1,
        'email' => '<EMAIL>',
        'user_id' => 1,
        'role' => 'member',
        'token' => md5(uniqid() . time()),
        'invited_by' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'expires_at' => date('Y-m-d H:i:s', strtotime('+7 days')),
        'status' => 'pending'
    );
    
    $result = $wpdb->insert($table_invitations, $test_data);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Test invitation inserted successfully</p>";
        
        // Clean up test data
        $wpdb->delete($table_invitations, array('email' => '<EMAIL>'));
        echo "<p style='color: blue;'>- Test data cleaned up</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to insert test invitation</p>";
        echo "<p style='color: red;'>Error: " . $wpdb->last_error . "</p>";
        echo "<p style='color: red;'>Query: " . $wpdb->last_query . "</p>";
    }
}

echo "<h3>3. Current Invitations</h3>";

if ($wpdb->get_var("SHOW TABLES LIKE '$table_invitations'") == $table_invitations) {
    $invitations = $wpdb->get_results("SELECT * FROM $table_invitations ORDER BY created_at DESC LIMIT 10");
    
    if (!empty($invitations)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Team ID</th><th>Email</th><th>Role</th><th>Status</th><th>Created</th><th>Expires</th></tr>";
        foreach ($invitations as $inv) {
            echo "<tr>";
            echo "<td>{$inv->id}</td>";
            echo "<td>{$inv->team_id}</td>";
            echo "<td>{$inv->email}</td>";
            echo "<td>{$inv->role}</td>";
            echo "<td>{$inv->status}</td>";
            echo "<td>{$inv->created_at}</td>";
            echo "<td>{$inv->expires_at}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No invitations found in database.</p>";
    }
}

echo "<h3>4. WordPress Functions Check</h3>";

// Check if WordPress functions are available
$functions_to_check = [
    'current_time',
    'get_current_user_id', 
    'wp_send_json_success',
    'wp_send_json_error',
    'check_ajax_referer',
    'get_user_by'
];

foreach ($functions_to_check as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✓ Function exists: $func</p>";
    } else {
        echo "<p style='color: red;'>✗ Function missing: $func</p>";
    }
}

echo "<h3>5. AJAX URL Check</h3>";
echo "<p>AJAX URL: " . admin_url('admin-ajax.php') . "</p>";

echo "<h3>6. Current User Info</h3>";
$current_user = wp_get_current_user();
echo "<p>Current User ID: " . $current_user->ID . "</p>";
echo "<p>Current User Email: " . $current_user->user_email . "</p>";
echo "<p>Current User Roles: " . implode(', ', $current_user->roles) . "</p>";

echo "<h3>7. Recommendations</h3>";
echo "<ul>";
echo "<li>If invitations table is missing, run update-invitations-table.php again</li>";
echo "<li>If WordPress functions are missing, there might be a plugin loading issue</li>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "<li>Check WordPress error logs for detailed error messages</li>";
echo "<li>Make sure you're testing with a valid user email that exists in WordPress</li>";
echo "</ul>";

echo "<p><strong>Note:</strong> Delete this file after debugging for security reasons.</p>";
?>
