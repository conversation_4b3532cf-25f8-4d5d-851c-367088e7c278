# نظام دعوات الفريق الذكي

## نظرة عامة
تم تطوير نظام دعوات ذكي وعصري للفرق يتطلب موافقة العضو المدعو قبل انضمامه للفريق، مع واجهة جميلة وتجربة مستخدم متقدمة.

## الميزات الجديدة

### 1. نظام الدعوات بدلاً من الإضافة المباشرة
- **قبل**: إضافة الأعضاء مباشرة للفريق
- **بعد**: إرسال دعوة تتطلب موافقة العضو

### 2. إدارة الدعوات للقائد
- عرض الدعوات المعلقة في صفحة تحرير الفريق
- إمكانية إلغاء الدعوات المرسلة
- معلومات تفصيلية عن كل دعوة (تاريخ الإرسال، انتهاء الصلاحية)

### 3. إشعارات جميلة للأعضاء المدعوين
- عرض الدعوات في الصفحة الرئيسية `/wp-admin/admin.php?page=team-system`
- تصميم عصري مع بطاقات متدرجة الألوان
- أزرار واضحة للقبول والرفض

### 4. إدارة انتهاء الصلاحية
- الدعوات تنتهي صلاحيتها خلال 7 أيام
- عدم عرض الدعوات المنتهية الصلاحية
- تنظيف تلقائي للدعوات القديمة

## كيفية عمل النظام

### للقائد (إرسال الدعوات):
1. اذهب إلى صفحة تحرير الفريق
2. انقر على تبويب "إدارة الأعضاء"
3. في قسم "دعوة أعضاء جدد":
   - أدخل البريد الإلكتروني للعضو
   - اختر الدور المطلوب
   - انقر "إرسال دعوة"
4. ستظهر الدعوة في قسم "الدعوات المعلقة"
5. يمكن إلغاء الدعوة في أي وقت

### للعضو المدعو (قبول/رفض الدعوات):
1. اذهب إلى `/wp-admin/admin.php?page=team-system`
2. ستظهر بطاقات الدعوات في أعلى الصفحة
3. كل بطاقة تحتوي على:
   - اسم الفريق
   - اسم الشخص الذي أرسل الدعوة
   - الدور المطلوب
   - تاريخ انتهاء الصلاحية
4. انقر "قبول الدعوة" أو "رفض الدعوة"

## التحديثات على قاعدة البيانات

### جدول team_invitations
```sql
CREATE TABLE wp_team_invitations (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    team_id bigint(20) NOT NULL,
    email varchar(100) NOT NULL,
    user_id bigint(20) DEFAULT NULL,
    role varchar(50) NOT NULL,
    token varchar(100) NOT NULL,
    invited_by bigint(20) NOT NULL,
    created_at datetime NOT NULL,
    expires_at datetime NOT NULL,
    status varchar(20) DEFAULT 'pending',
    PRIMARY KEY (id),
    UNIQUE KEY token (token)
);
```

### حالات الدعوة (status):
- `pending`: في انتظار الرد
- `accepted`: تم القبول
- `declined`: تم الرفض
- `cancelled`: تم الإلغاء من القائد
- `expired`: انتهت الصلاحية

## الملفات المُحدثة

### 1. admin/class-team-system-admin.php
- تحديث `ajax_add_member()` لإرسال دعوات بدلاً من الإضافة المباشرة
- إضافة `ajax_accept_invitation()`
- إضافة `ajax_decline_invitation()`
- إضافة `ajax_cancel_invitation()`
- تحديث إنشاء جدول `team_invitations`

### 2. admin/partials/team-system-edit-team.php
- إضافة قسم "الدعوات المعلقة"
- تحديث النص من "إضافة أعضاء" إلى "دعوة أعضاء"
- إضافة JavaScript للتعامل مع إلغاء الدعوات
- تنسيق CSS جميل للدعوات المعلقة

### 3. admin/partials/team-system-admin-display.php
- إضافة قسم عرض الدعوات للمستخدم الحالي
- بطاقات دعوات بتصميم عصري
- JavaScript للتعامل مع قبول/رفض الدعوات
- تنسيق CSS متقدم مع تأثيرات بصرية

## التصميم والواجهة

### بطاقات الدعوات:
- خلفية متدرجة جميلة (أزرق إلى بنفسجي)
- تأثيرات hover مع رفع البطاقة
- معلومات واضحة ومنظمة
- أزرار ملونة للإجراءات

### الدعوات المعلقة (للقائد):
- خلفية رمادية فاتحة
- صفوف منظمة مع معلومات مفصلة
- حالة "في انتظار الرد" واضحة
- زر إلغاء أحمر

### رسائل التأكيد:
- رسائل نجاح خضراء
- رسائل خطأ حمراء
- تأثيرات fade للانتقالات

## الأمان والتحقق

### التحقق من الصلاحيات:
- التأكد من أن المستخدم هو قائد الفريق لإرسال الدعوات
- التأكد من أن المستخدم هو المدعو لقبول/رفض الدعوة
- استخدام nonce للحماية من CSRF

### التحقق من البيانات:
- التأكد من وجود المستخدم بالبريد الإلكتروني
- منع الدعوات المكررة
- التحقق من انتهاء الصلاحية

## حالات الاستخدام

### سيناريو 1: دعوة ناجحة
1. القائد يرسل دعوة → حالة `pending`
2. العضو يقبل الدعوة → حالة `accepted` + إضافة للفريق
3. الدعوة تختفي من كلا الطرفين

### سيناريو 2: رفض الدعوة
1. القائد يرسل دعوة → حالة `pending`
2. العضو يرفض الدعوة → حالة `declined`
3. الدعوة تختفي من العضو، تبقى في سجل القائد

### سيناريو 3: إلغاء الدعوة
1. القائد يرسل دعوة → حالة `pending`
2. القائد يلغي الدعوة → حالة `cancelled`
3. الدعوة تختفي من كلا الطرفين

### سيناريو 4: انتهاء الصلاحية
1. القائد يرسل دعوة → حالة `pending`
2. مرور 7 أيام → الدعوة لا تظهر للعضو
3. النظام يتجاهل الدعوات المنتهية الصلاحية

## المزايا الجديدة

### للقادة:
- تحكم كامل في الدعوات
- معلومات واضحة عن حالة كل دعوة
- إمكانية إلغاء الدعوات غير المرغوبة

### للأعضاء:
- حرية الاختيار في الانضمام
- واجهة جميلة وواضحة
- معلومات كاملة عن الفريق والدور

### للنظام:
- أمان أكبر ضد الإضافات غير المرغوبة
- تتبع أفضل للعضوية
- تجربة مستخدم محسنة

## الخطوات التالية المقترحة
1. إضافة إشعارات بريد إلكتروني للدعوات
2. إضافة نظام إشعارات داخل الموقع
3. إحصائيات الدعوات للقادة
4. تذكيرات للدعوات المعلقة
