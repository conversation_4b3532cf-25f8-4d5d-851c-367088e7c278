# حل مشكلة "حدث خطأ أثناء إرسال الدعوة"

## خطوات التشخيص والحل

### الخطوة 1: تشغيل سكريبت التشخيص
1. افتح المتصفح واذهب إلى: `http://localhost/wp-content/plugins/team-system/debug-invitations.php`
2. تحقق من النتائج:
   - هل جدول `team_invitations` موجود؟
   - هل تم إدراج البيانات التجريبية بنجاح؟
   - هل جميع دوال WordPress متاحة؟

### الخطوة 2: فحص أخطاء المتصفح
1. افتح Developer Tools في المتصفح (F12)
2. اذهب إلى تبويب Console
3. جرب إرسال دعوة ولاحظ أي أخطاء JavaScript

### الخطوة 3: فحص أخطاء WordPress
1. تفعيل debug في WordPress:
   ```php
   // في wp-config.php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   ```
2. تحقق من ملف `/wp-content/debug.log` بعد محاولة إرسال دعوة

### الخطوة 4: التحقق من البيانات المُرسلة
افتح Network tab في Developer Tools وتحقق من:
- هل يتم إرسال الطلب إلى `admin-ajax.php`؟
- ما هي البيانات المُرسلة؟
- ما هو الرد من الخادم؟

## الأسباب المحتملة والحلول

### 1. جدول team_invitations غير موجود
**الحل**: تشغيل `update-invitations-table.php` مرة أخرى

### 2. مشكلة في nonce
**الأعراض**: رسالة خطأ تتعلق بـ nonce verification
**الحل**: تم إصلاح هذا بإضافة تعريف `teamSystemAjax` في الصفحات

### 3. مشكلة في دوال WordPress
**الأعراض**: أخطاء تتعلق بـ `current_time` أو `get_current_user_id`
**الحل**: التأكد من تحميل WordPress بشكل صحيح

### 4. مشكلة في بنية قاعدة البيانات
**الأعراض**: خطأ SQL في الـ logs
**الحل**: التحقق من أن جميع الأعمدة موجودة في الجدول

### 5. مشكلة في البريد الإلكتروني
**الأعراض**: "لم يتم العثور على مستخدم بهذا البريد الإلكتروني"
**الحل**: التأكد من أن البريد الإلكتروني موجود في WordPress

## اختبار سريع

### اختبار 1: التحقق من AJAX
```javascript
// في console المتصفح
jQuery.post(ajaxurl, {
    action: 'team_system_add_member',
    team_id: 1,
    email: '<EMAIL>',
    role: 'member',
    nonce: teamSystemAjax.nonce
}, function(response) {
    console.log(response);
});
```

### اختبار 2: التحقق من قاعدة البيانات
```sql
-- في phpMyAdmin أو أي أداة قاعدة بيانات
SELECT * FROM wp_team_invitations;
DESCRIBE wp_team_invitations;
```

## الإصلاحات المُطبقة

### 1. إصلاح token generation
- استبدال `wp_generate_password` بـ `md5(uniqid())`
- إضافة معالجة أخطاء أفضل

### 2. إصلاح JavaScript variables
- إضافة تعريف `ajaxurl` و `teamSystemAjax` في الصفحات
- التأكد من توفر nonce

### 3. إضافة logging للتشخيص
- تسجيل الأخطاء في WordPress error log
- عرض رسائل خطأ أكثر تفصيلاً

## خطوات الاختبار النهائي

1. **تأكد من تشغيل سكريبت التحديث**:
   ```
   http://localhost/wp-content/plugins/team-system/update-invitations-table.php
   ```

2. **اذهب إلى صفحة تحرير فريق**:
   ```
   /wp-admin/admin.php?page=team-system&action=edit&team_id=1
   ```

3. **جرب إرسال دعوة**:
   - أدخل بريد إلكتروني لمستخدم موجود
   - اختر دور
   - انقر "إرسال دعوة"

4. **تحقق من النتيجة**:
   - يجب أن تظهر رسالة "تم إرسال دعوة الانضمام للفريق بنجاح"
   - يجب أن تظهر الدعوة في قسم "الدعوات المعلقة"

## إذا استمرت المشكلة

1. **تحقق من error log**:
   ```
   tail -f /path/to/wordpress/wp-content/debug.log
   ```

2. **تشغيل سكريبت التشخيص**:
   ```
   http://localhost/wp-content/plugins/team-system/debug-invitations.php
   ```

3. **فحص Network tab** في Developer Tools

4. **التأكد من صلاحيات قاعدة البيانات**

## ملاحظات أمنية
- احذف ملفات التشخيص بعد الانتهاء:
  - `debug-invitations.php`
  - `update-invitations-table.php`
- تأكد من إيقاف WP_DEBUG في الإنتاج
