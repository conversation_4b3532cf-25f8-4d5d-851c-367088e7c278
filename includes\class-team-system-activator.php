<?php
class Team_System_Activator {
    public static function register_rewrite_rules() {
        // Add rewrite rules for teams
        add_rewrite_rule('^teams/([^/]+)/?$', 'index.php?team_slug=$matches[1]', 'top');
        add_rewrite_rule('^teams/page/([0-9]{1,})/?$', 'index.php?pagename=teams&paged=$matches[1]', 'top');
        
        // Add team_slug to query vars
        add_filter('query_vars', function($vars) {
            $vars[] = 'team_slug';
            return $vars;
        });
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public static function activate() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();
        $table_name = $wpdb->prefix . 'teams';

        // Register rewrite rules
        self::register_rewrite_rules();

        // Create teams table
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            slug varchar(100) NOT NULL,
            description text,
            logo_url varchar(255),
            cover_url varchar(255),
            website_url varchar(255),
            facebook_url varchar(255),
            twitter_url varchar(255),
            discord_url varchar(255),
            privacy varchar(20) DEFAULT 'public',
            created_by bigint(20) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            UNIQUE KEY slug (slug)
        ) $charset_collate;";

        $table_name = $wpdb->prefix . 'team_members';
        $sql .= "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            team_id mediumint(9) NOT NULL,
            user_id bigint(20) NOT NULL,
            role varchar(50) NOT NULL,
            joined_at datetime DEFAULT CURRENT_TIMESTAMP,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY  (id),
            UNIQUE KEY team_user (team_id, user_id)
        ) $charset_collate;";

        $table_name = $wpdb->prefix . 'team_invitations';
        $sql .= "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            team_id mediumint(9) NOT NULL,
            email varchar(100) NOT NULL,
            role varchar(50) NOT NULL,
            token varchar(100) NOT NULL,
            invited_by bigint(20) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            expires_at datetime NOT NULL,
            status varchar(20) DEFAULT 'pending',
            PRIMARY KEY  (id),
            UNIQUE KEY token (token)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Add capabilities
        $admin_role = get_role('administrator');
        $admin_role->add_cap('manage_teams');
        $admin_role->add_cap('edit_teams');
        $admin_role->add_cap('delete_teams');
        $admin_role->add_cap('publish_teams');
        $admin_role->add_cap('edit_others_teams');

        $translator_role = get_role('translator');
        if ($translator_role) {
            $translator_role->add_cap('create_teams');
            $translator_role->add_cap('edit_own_team');
            $translator_role->add_cap('manage_team_members');
        }
    }

    public static function deactivate() {
        // Clean up rewrite rules
        flush_rewrite_rules();
        
        // Remove capabilities
        $admin_role = get_role('administrator');
        if ($admin_role) {
            $admin_role->remove_cap('manage_teams');
            $admin_role->remove_cap('edit_teams');
            $admin_role->remove_cap('delete_teams');
            $admin_role->remove_cap('publish_teams');
            $admin_role->remove_cap('edit_others_teams');
        }
        
        $translator_role = get_role('translator');
        if ($translator_role) {
            $translator_role->remove_cap('create_teams');
            $translator_role->remove_cap('edit_own_team');
            $translator_role->remove_cap('manage_team_members');
        }
    }
}