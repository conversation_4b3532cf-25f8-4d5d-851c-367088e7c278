<?php
/**
 * Database update script for Team System
 * Run this file once to add missing social media columns to the teams table
 */

// Include WordPress
require_once('../../../wp-config.php');

global $wpdb;

$table_teams = $wpdb->prefix . 'teams';

// Check if table exists
if ($wpdb->get_var("SHOW TABLES LIKE '$table_teams'") != $table_teams) {
    die("Table $table_teams does not exist.");
}

echo "<h2>Team System Database Update</h2>";
echo "<p>Updating database structure...</p>";

// Add social media columns if they don't exist
$social_columns = array(
    'website_url' => 'VARCHAR(255) DEFAULT NULL',
    'facebook_url' => 'VARCHAR(255) DEFAULT NULL',
    'twitter_url' => 'VARCHAR(255) DEFAULT NULL',
    'discord_url' => 'VARCHAR(255) DEFAULT NULL'
);

$updated = false;

foreach ($social_columns as $column => $definition) {
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_teams LIKE '$column'");
    if (empty($column_exists)) {
        $result = $wpdb->query("ALTER TABLE $table_teams ADD COLUMN $column $definition");
        if ($result !== false) {
            echo "<p style='color: green;'>✓ Added column: $column</p>";
            $updated = true;
        } else {
            echo "<p style='color: red;'>✗ Failed to add column: $column - " . $wpdb->last_error . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>- Column already exists: $column</p>";
    }
}

if ($updated) {
    echo "<p style='color: green; font-weight: bold;'>Database update completed successfully!</p>";
} else {
    echo "<p style='color: blue; font-weight: bold;'>No updates were needed. Database is already up to date.</p>";
}

echo "<p><strong>Note:</strong> You can now delete this file for security reasons.</p>";
?>
/**
 * Database update script for Team System
 * Run this file once to add missing social media columns to the teams table
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

global $wpdb;

$table_teams = $wpdb->prefix . 'teams';

// Check if table exists
if ($wpdb->get_var("SHOW TABLES LIKE '$table_teams'") != $table_teams) {
    die("Table $table_teams does not exist.");
}

echo "<h2>Team System Database Update</h2>";
echo "<p>Updating database structure...</p>";

// Add social media columns if they don't exist
$social_columns = array(
    'website_url' => 'VARCHAR(255) DEFAULT NULL',
    'facebook_url' => 'VARCHAR(255) DEFAULT NULL', 
    'twitter_url' => 'VARCHAR(255) DEFAULT NULL',
    'discord_url' => 'VARCHAR(255) DEFAULT NULL'
);

$updated = false;

foreach ($social_columns as $column => $definition) {
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM $table_teams LIKE '$column'");
    if (empty($column_exists)) {
        $result = $wpdb->query("ALTER TABLE $table_teams ADD COLUMN $column $definition");
        if ($result !== false) {
            echo "<p style='color: green;'>✓ Added column: $column</p>";
            $updated = true;
        } else {
            echo "<p style='color: red;'>✗ Failed to add column: $column - " . $wpdb->last_error . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>- Column already exists: $column</p>";
    }
}

if ($updated) {
    echo "<p style='color: green; font-weight: bold;'>Database update completed successfully!</p>";
} else {
    echo "<p style='color: blue; font-weight: bold;'>No updates were needed. Database is already up to date.</p>";
}

echo "<p><strong>Note:</strong> You can now delete this file for security reasons.</p>";
?>
