# حل مشكلة إعدادات الفريق - عدم حفظ البيانات

## المشكلة
إعدادات الفريق في صفحة `/wp-admin/admin.php?page=team-system&action=edit` لا تحفظ المدخلات ولا تُطبق على صفحة عرض بيانات الفريق.

## السبب
المشكلة الأساسية هي أن عمود `privacy` غير موجود في جدول `teams` في قاعدة البيانات، مما يسبب فشل في عملية التحديث.

## الحل

### الخطوة 1: تشغيل سكريبت تحديث قاعدة البيانات
1. افتح المتصفح واذهب إلى: `http://localhost/wp-content/plugins/team-system/update-privacy-column.php`
   (أو استبدل localhost بنطاق موقعك)
2. تأكد من تسجيل الدخول كمدير
3. ستظهر رسالة تأكيد إضافة عمود `privacy`
4. احذف الملف `update-privacy-column.php` بعد التشغيل لأسباب أمنية

### الخطوة 2: التحقق من النتائج
1. اذهب إلى صفحة تحرير الفريق: `/wp-admin/admin.php?page=team-system&action=edit&team_id=X`
2. انقر على تبويب "الإعدادات"
3. املأ حقول الإعدادات:
   - الموقع الإلكتروني: `https://example.com`
   - فيسبوك: `https://facebook.com/teamname`
   - تويتر: `https://twitter.com/teamname`
   - ديسكورد: `https://discord.gg/teamname`
   - خصوصية الفريق: اختر أحد الخيارات
4. اضغط "حفظ التغييرات"
5. يجب أن تظهر رسالة "تم تحديث بيانات الفريق بنجاح"
6. تحقق من ظهور الروابط في صفحة عرض الفريق

## التحسينات المُطبقة

### 1. إضافة عمود privacy إلى قاعدة البيانات
```sql
ALTER TABLE wp_teams ADD COLUMN privacy VARCHAR(20) DEFAULT 'public';
```

### 2. تحديث دالة update_team
- إصلاح مشكلة عدم تطابق معاملات wpdb->update
- إضافة معالجة أخطاء أفضل
- تسجيل الأخطاء في log للتشخيص

### 3. تحديث جداول إنشاء قاعدة البيانات
- إضافة عمود privacy في class-team-system-activator.php
- إضافة عمود privacy في class-team-system-admin.php

## اختبار الحل

### 1. اختبار حفظ الإعدادات
```
1. اذهب إلى: /wp-admin/admin.php?page=team-system&action=edit&team_id=X
2. انقر على تبويب "الإعدادات"
3. املأ الحقول التالية:
   - الموقع الإلكتروني: https://example.com
   - فيسبوك: https://facebook.com/teamname
   - تويتر: https://twitter.com/teamname
   - ديسكورد: https://discord.gg/teamname
   - خصوصية الفريق: اختر أحد الخيارات
4. اضغط "حفظ التغييرات"
5. تحقق من ظهور رسالة "تم تحديث بيانات الفريق بنجاح"
```

### 2. اختبار عرض الإعدادات
```
1. اذهب إلى صفحة عرض الفريق
2. تحقق من ظهور الروابط الاجتماعية
3. تحقق من تطبيق إعدادات الخصوصية
```

## ملاحظات مهمة

### خيارات الخصوصية
- **عام**: يمكن للجميع رؤية الفريق وأعضائه
- **خاص**: يمكن للجميع رؤية الفريق ولكن الأعضاء فقط هم من يمكنهم رؤية الأعضاء  
- **مخفي**: لا يمكن لأحد رؤية الفريق سوى الأعضاء

### استكشاف الأخطاء
إذا استمرت المشكلة:
1. تحقق من error log في WordPress
2. تأكد من وجود عمود privacy في جدول teams
3. تحقق من صلاحيات المستخدم
4. تأكد من عمل nonce verification

## الملفات المُحدثة
- `admin/class-team-system-admin.php` - إصلاح دالة update_team وإضافة عمود privacy
- `includes/class-team-system-activator.php` - إضافة عمود privacy لإنشاء الجداول الجديدة
- `update-privacy-column.php` - سكريبت مؤقت لتحديث قاعدة البيانات الموجودة

## كيفية عمل الحل

### قبل الإصلاح:
```
POST data: team_privacy = "private"
Database: لا يوجد عمود privacy
Result: wpdb->update() يفشل صامتاً
Effect: لا يتم حفظ أي من البيانات
```

### بعد الإصلاح:
```
POST data: team_privacy = "private"
Database: عمود privacy موجود
Result: wpdb->update() ينجح
Effect: جميع البيانات تُحفظ بنجاح
```

## التحقق من نجاح الحل
يمكنك التحقق من نجاح الحل عبر:

1. **فحص قاعدة البيانات مباشرة**:
```sql
DESCRIBE wp_teams;
-- يجب أن ترى عمود privacy في النتائج
```

2. **فحص البيانات المحفوظة**:
```sql
SELECT name, website_url, facebook_url, twitter_url, discord_url, privacy
FROM wp_teams WHERE id = YOUR_TEAM_ID;
```

3. **فحص error log**:
   - إذا استمرت المشكلة، ستجد تفاصيل الخطأ في WordPress error log
