<?php
/**
 * Database update script for Team System - Privacy Column
 * Run this file once to add the missing privacy column to the teams table
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

global $wpdb;

$table_teams = $wpdb->prefix . 'teams';

// Check if table exists
if ($wpdb->get_var("SHOW TABLES LIKE '$table_teams'") != $table_teams) {
    die("Table $table_teams does not exist.");
}

echo "<h2>Team System Database Update - Privacy Column</h2>";
echo "<p>Adding privacy column to teams table...</p>";

// Check if privacy column exists
$privacy_column = $wpdb->get_results("SHOW COLUMNS FROM $table_teams LIKE 'privacy'");

if (empty($privacy_column)) {
    // Add privacy column
    $result = $wpdb->query("ALTER TABLE $table_teams ADD COLUMN privacy VARCHAR(20) DEFAULT 'public'");
    
    if ($result !== false) {
        echo "<p style='color: green;'>✓ Added privacy column successfully</p>";
        
        // Update existing teams to have 'public' privacy by default
        $update_result = $wpdb->query("UPDATE $table_teams SET privacy = 'public' WHERE privacy IS NULL OR privacy = ''");
        
        if ($update_result !== false) {
            echo "<p style='color: green;'>✓ Updated existing teams with default privacy setting</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Warning: Could not update existing teams - " . $wpdb->last_error . "</p>";
        }
        
        echo "<p style='color: green; font-weight: bold;'>Privacy column update completed successfully!</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to add privacy column - " . $wpdb->last_error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>- Privacy column already exists</p>";
    echo "<p style='color: blue; font-weight: bold;'>No updates were needed. Database is already up to date.</p>";
}

echo "<p><strong>Note:</strong> You can now delete this file for security reasons.</p>";
echo "<p><strong>Next steps:</strong> Go to your team edit page and test the privacy settings.</p>";
?>
