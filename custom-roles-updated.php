<?php
/**
 * Custom Roles for Sekaiplus Theme
 * Updated to include Team System capabilities for translator role
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add custom roles and capabilities
 */
function sekaiplus_add_custom_roles() {
    // Get the translator role or create it if it doesn't exist
    $translator_role = get_role('translator');
    
    if (!$translator_role) {
        // Create translator role if it doesn't exist
        add_role(
            'translator',
            __('Translator', 'sekaiplus'),
            array(
                // Basic WordPress capabilities
                'read' => true,
                'edit_posts' => true,
                'upload_files' => true,
                
                // Team System capabilities
                'access_team_system' => true,
                'create_teams' => true,
                'join_teams' => true,
                'edit_own_team' => true,
                'manage_team_members' => true,
                
                // Add any other translator-specific capabilities here
                'translate_content' => true,
                'edit_translations' => true,
            )
        );
    } else {
        // If role exists, make sure it has all required capabilities
        $translator_capabilities = array(
            // Basic WordPress capabilities
            'read' => true,
            'edit_posts' => true,
            'upload_files' => true,
            
            // Team System capabilities
            'access_team_system' => true,
            'create_teams' => true,
            'join_teams' => true,
            'edit_own_team' => true,
            'manage_team_members' => true,
            
            // Translator-specific capabilities
            'translate_content' => true,
            'edit_translations' => true,
        );
        
        // Add each capability to the existing role
        foreach ($translator_capabilities as $cap => $grant) {
            $translator_role->add_cap($cap, $grant);
        }
    }
    
    // Also ensure other roles have team system access
    $roles_with_team_access = array(
        'administrator' => array(
            'access_team_system' => true,
            'create_teams' => true,
            'join_teams' => true,
            'manage_teams' => true,
            'edit_teams' => true,
            'delete_teams' => true,
            'edit_others_teams' => true,
        ),
        'editor' => array(
            'access_team_system' => true,
            'create_teams' => true,
            'join_teams' => true,
            'edit_own_team' => true,
        ),
        'author' => array(
            'access_team_system' => true,
            'create_teams' => true,
            'join_teams' => true,
        ),
    );
    
    foreach ($roles_with_team_access as $role_name => $capabilities) {
        $role = get_role($role_name);
        if ($role) {
            foreach ($capabilities as $cap => $grant) {
                $role->add_cap($cap, $grant);
            }
        }
    }
}

/**
 * Remove custom roles and capabilities on theme deactivation
 */
function sekaiplus_remove_custom_roles() {
    // Note: Usually we don't remove roles on theme deactivation
    // because users might lose access. Only remove if absolutely necessary.
    
    // If you want to remove the translator role completely:
    // remove_role('translator');
    
    // Or just remove specific capabilities:
    $translator_role = get_role('translator');
    if ($translator_role) {
        // Remove only theme-specific capabilities, keep team system ones
        $translator_role->remove_cap('translate_content');
        $translator_role->remove_cap('edit_translations');
    }
}

/**
 * Initialize custom roles
 * Use init hook with high priority to ensure it runs after plugins
 */
add_action('init', 'sekaiplus_add_custom_roles', 999);

/**
 * Alternative: Use wp_roles_init hook if available (WordPress 4.7+)
 */
if (function_exists('wp_roles_init')) {
    add_action('wp_roles_init', 'sekaiplus_add_custom_roles', 999);
}

/**
 * Ensure capabilities are added after theme activation
 */
add_action('after_switch_theme', 'sekaiplus_add_custom_roles');

/**
 * Optional: Add capabilities check on admin_init for extra safety
 */
function sekaiplus_ensure_translator_capabilities() {
    // Only run for admin users to avoid performance issues
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Check if translator role has required team system capabilities
    $translator_role = get_role('translator');
    if ($translator_role) {
        $required_caps = array('access_team_system', 'create_teams', 'join_teams', 'edit_posts');
        $missing_caps = array();
        
        foreach ($required_caps as $cap) {
            if (!isset($translator_role->capabilities[$cap]) || !$translator_role->capabilities[$cap]) {
                $missing_caps[] = $cap;
            }
        }
        
        // If any capabilities are missing, add them
        if (!empty($missing_caps)) {
            foreach ($missing_caps as $cap) {
                $translator_role->add_cap($cap);
            }
            
            // Log for debugging (optional)
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Sekaiplus Theme: Added missing translator capabilities: ' . implode(', ', $missing_caps));
            }
        }
    }
}

// Run capability check on admin_init with low priority
add_action('admin_init', 'sekaiplus_ensure_translator_capabilities', 999);

/**
 * Function to check if user can access team system
 * This can be used in theme templates
 */
function sekaiplus_user_can_access_teams($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    
    return user_can($user_id, 'access_team_system') || user_can($user_id, 'manage_options');
}

/**
 * Get user's team role display name
 */
function sekaiplus_get_team_role_name($role) {
    $role_names = array(
        'leader' => __('Team Leader', 'sekaiplus'),
        'translator' => __('Translator', 'sekaiplus'),
        'editor' => __('Editor', 'sekaiplus'),
        'reviewer' => __('Reviewer', 'sekaiplus'),
        'designer' => __('Designer', 'sekaiplus'),
    );
    
    return isset($role_names[$role]) ? $role_names[$role] : $role;
}

/**
 * Debug function to display current user capabilities (for development only)
 */
function sekaiplus_debug_user_capabilities() {
    if (!defined('WP_DEBUG') || !WP_DEBUG || !current_user_can('manage_options')) {
        return;
    }
    
    $current_user = wp_get_current_user();
    echo '<div style="background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;">';
    echo '<h4>Debug: Current User Capabilities</h4>';
    echo '<p><strong>User:</strong> ' . $current_user->user_login . '</p>';
    echo '<p><strong>Roles:</strong> ' . implode(', ', $current_user->roles) . '</p>';
    echo '<p><strong>Team System Access:</strong> ' . (current_user_can('access_team_system') ? 'YES' : 'NO') . '</p>';
    echo '</div>';
}

// Uncomment the line below for debugging (remove in production)
// add_action('wp_footer', 'sekaiplus_debug_user_capabilities');
// add_action('admin_footer', 'sekaiplus_debug_user_capabilities');
?>
