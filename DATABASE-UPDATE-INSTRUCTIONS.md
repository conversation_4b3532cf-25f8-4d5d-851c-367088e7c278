# تعليمات تحديث قاعدة البيانات لنظام الفرق

## المشكلة
صفحة بيانات الفريق تظهر تحذيرات بسبب عدم وجود أعمدة وسائل التواصل الاجتماعي في جدول `teams`.

## الحل
تشغيل سكريبت تحديث قاعدة البيانات لإضافة الأعمدة المفقودة.

## خطوات التحديث

### الطريقة الأولى: تشغيل السكريبت عبر المتصفح
1. افتح المتصفح واذهب إلى: `http://localhost/wp/wp-content/plugins/team-system/team-system-db-update.php`
2. ستظهر لك صفحة تحديث قاعدة البيانات
3. السكريبت سيضيف الأعمدة المفقودة تلقائياً
4. احذف الملف `team-system-db-update.php` بعد التحديث لأسباب أمنية

### الطريقة الثانية: تشغيل SQL مباشرة
قم بتشغيل الاستعلامات التالية في phpMyAdmin أو أي أداة إدارة قاعدة بيانات:

```sql
ALTER TABLE wp_teams ADD COLUMN website_url VARCHAR(255) DEFAULT NULL;
ALTER TABLE wp_teams ADD COLUMN facebook_url VARCHAR(255) DEFAULT NULL;
ALTER TABLE wp_teams ADD COLUMN twitter_url VARCHAR(255) DEFAULT NULL;
ALTER TABLE wp_teams ADD COLUMN discord_url VARCHAR(255) DEFAULT NULL;
```

**ملاحظة:** استبدل `wp_` ببادئة قاعدة البيانات الخاصة بك إذا كانت مختلفة.

## التحقق من نجاح التحديث
بعد تشغيل التحديث، تأكد من:
1. عدم ظهور تحذيرات في صفحة بيانات الفريق
2. ظهور روابط وسائل التواصل الاجتماعي بشكل صحيح
3. عمل جميع وظائف الصفحة بشكل طبيعي

## الأعمدة المضافة
- `website_url`: رابط الموقع الرسمي للفريق
- `facebook_url`: رابط صفحة فيسبوك
- `twitter_url`: رابط حساب تويتر
- `discord_url`: رابط خادم ديسكورد

## ملاحظات مهمة
- تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التحديث
- السكريبت آمن ولن يؤثر على البيانات الموجودة
- يمكن تشغيل السكريبت عدة مرات دون مشاكل (سيتجاهل الأعمدة الموجودة)
