<?php
/**
 * Trace Redirect Issue for Team System
 * This script will help us trace exactly what's happening when translator accesses team-system page
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

echo "<h2>🔍 Tracing Team System Redirect Issue</h2>";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check 1: Test direct access to the display function
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0073aa; margin: 10px 0;'>";
echo "<h3>1. Testing Direct Function Access</h3>";

// Create admin instance
require_once(plugin_dir_path(__FILE__) . 'admin/class-team-system-admin.php');
$admin = new Team_System_Admin('team-system', '1.0.0');

echo "<p><strong>Admin class created successfully:</strong> ✓</p>";

// Test get_required_capability function
try {
    // We need to use reflection to access private method
    $reflection = new ReflectionClass($admin);
    $method = $reflection->getMethod('get_required_capability');
    $method->setAccessible(true);
    $required_cap = $method->invoke($admin);
    
    echo "<p><strong>Required capability for current user:</strong> $required_cap</p>";
    echo "<p><strong>Current user can access:</strong> " . (current_user_can($required_cap) ? '✓ YES' : '✗ NO') . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error testing capability:</strong> " . $e->getMessage() . "</p>";
}

echo "</div>";

// Check 2: Test with translator user
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
echo "<h3>2. Testing with Translator User</h3>";

$translator_users = get_users(array('role' => 'translator', 'number' => 1));
if (!empty($translator_users)) {
    $translator = $translator_users[0];
    echo "<p><strong>Test translator:</strong> " . $translator->user_login . "</p>";
    
    // Test capabilities for translator
    $test_caps = array('access_team_system', 'create_teams', 'join_teams', 'edit_posts', 'manage_options');
    foreach ($test_caps as $cap) {
        $has_cap = user_can($translator, $cap);
        $status = $has_cap ? '✓' : '✗';
        $color = $has_cap ? 'green' : 'red';
        echo "<p style='color: $color;'>$status <strong>$cap</strong></p>";
    }
    
    // Test required capability for translator
    // Simulate the logic
    if (user_can($translator, 'manage_options')) {
        $translator_required_cap = 'manage_options';
    } elseif (user_can($translator, 'access_team_system')) {
        $translator_required_cap = 'access_team_system';
    } elseif (user_can($translator, 'edit_others_posts')) {
        $translator_required_cap = 'edit_others_posts';
    } elseif (user_can($translator, 'publish_posts')) {
        $translator_required_cap = 'publish_posts';
    } else {
        $translator_required_cap = 'edit_posts';
    }
    
    echo "<p><strong>Required capability for translator:</strong> $translator_required_cap</p>";
    echo "<p><strong>Translator can access:</strong> " . (user_can($translator, $translator_required_cap) ? '✓ YES' : '✗ NO') . "</p>";
} else {
    echo "<p style='color: orange;'>⚠ No translator users found</p>";
}

echo "</div>";

// Check 3: Test database tables
echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #0073aa; margin: 10px 0;'>";
echo "<h3>3. Database Tables Check</h3>";

global $wpdb;

// Check teams table
$teams_table = $wpdb->prefix . 'teams';
$teams_exists = $wpdb->get_var("SHOW TABLES LIKE '$teams_table'") == $teams_table;
echo "<p><strong>Teams table exists:</strong> " . ($teams_exists ? '✓ YES' : '✗ NO') . "</p>";

if ($teams_exists) {
    $teams_count = $wpdb->get_var("SELECT COUNT(*) FROM $teams_table");
    echo "<p><strong>Teams count:</strong> $teams_count</p>";
}

// Check team_members table
$members_table = $wpdb->prefix . 'team_members';
$members_exists = $wpdb->get_var("SHOW TABLES LIKE '$members_table'") == $members_table;
echo "<p><strong>Team members table exists:</strong> " . ($members_exists ? '✓ YES' : '✗ NO') . "</p>";

// Check team_invitations table
$invitations_table = $wpdb->prefix . 'team_invitations';
$invitations_exists = $wpdb->get_var("SHOW TABLES LIKE '$invitations_table'") == $invitations_table;
echo "<p><strong>Team invitations table exists:</strong> " . ($invitations_exists ? '✓ YES' : '✗ NO') . "</p>";

echo "</div>";

// Check 4: Test WordPress admin menu system
echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
echo "<h3>4. WordPress Admin Menu Test</h3>";

// Check if the menu is registered
global $menu, $submenu;

$team_menu_found = false;
if (is_array($menu)) {
    foreach ($menu as $menu_item) {
        if (isset($menu_item[2]) && $menu_item[2] === 'team-system') {
            $team_menu_found = true;
            echo "<p><strong>Team System menu found:</strong> ✓ YES</p>";
            echo "<p><strong>Menu title:</strong> " . $menu_item[0] . "</p>";
            echo "<p><strong>Required capability:</strong> " . $menu_item[1] . "</p>";
            echo "<p><strong>Menu slug:</strong> " . $menu_item[2] . "</p>";
            break;
        }
    }
}

if (!$team_menu_found) {
    echo "<p style='color: red;'><strong>Team System menu:</strong> ✗ NOT FOUND</p>";
}

echo "</div>";

// Check 5: Test file includes
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; margin: 10px 0;'>";
echo "<h3>5. File Includes Test</h3>";

$admin_display_file = plugin_dir_path(__FILE__) . 'admin/partials/team-system-admin-display.php';
echo "<p><strong>Admin display file exists:</strong> " . (file_exists($admin_display_file) ? '✓ YES' : '✗ NO') . "</p>";
echo "<p><strong>File path:</strong> $admin_display_file</p>";

if (file_exists($admin_display_file)) {
    echo "<p><strong>File is readable:</strong> " . (is_readable($admin_display_file) ? '✓ YES' : '✗ NO') . "</p>";
    echo "<p><strong>File size:</strong> " . filesize($admin_display_file) . " bytes</p>";
}

echo "</div>";

// Check 6: Test for PHP errors
echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
echo "<h3>6. PHP Error Check</h3>";

// Capture any output from including the admin display file
ob_start();
$error_occurred = false;

try {
    // Set error handler to catch any errors
    set_error_handler(function($severity, $message, $file, $line) use (&$error_occurred) {
        $error_occurred = true;
        echo "<p style='color: red;'><strong>PHP Error:</strong> $message in $file on line $line</p>";
    });
    
    // Try to include the admin display file
    if (file_exists($admin_display_file)) {
        include $admin_display_file;
    }
    
    restore_error_handler();
} catch (Exception $e) {
    $error_occurred = true;
    echo "<p style='color: red;'><strong>Exception:</strong> " . $e->getMessage() . "</p>";
}

$output = ob_get_clean();

if (!$error_occurred) {
    echo "<p style='color: green;'><strong>No PHP errors detected</strong> ✓</p>";
} else {
    echo "<p style='color: red;'><strong>PHP errors detected</strong> ✗</p>";
}

if (!empty($output)) {
    echo "<h4>File Output Preview:</h4>";
    echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 200px; overflow-y: auto;'>";
    echo "<pre>" . htmlspecialchars(substr($output, 0, 1000)) . "</pre>";
    if (strlen($output) > 1000) {
        echo "<p><em>... (output truncated)</em></p>";
    }
    echo "</div>";
}

echo "</div>";

// Recommendations
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #0073aa; margin: 20px 0;'>";
echo "<h3>🔧 Debugging Recommendations</h3>";
echo "<ol>";
echo "<li><strong>Enable WordPress Debug Mode:</strong> Add these lines to wp-config.php:";
echo "<pre>define('WP_DEBUG', true);\ndefine('WP_DEBUG_LOG', true);\ndefine('WP_DEBUG_DISPLAY', false);</pre></li>";
echo "<li><strong>Check WordPress Error Log:</strong> Look in /wp-content/debug.log for any errors</li>";
echo "<li><strong>Test with different user:</strong> Try accessing as admin first, then as translator</li>";
echo "<li><strong>Check browser console:</strong> Look for JavaScript errors that might cause redirects</li>";
echo "<li><strong>Check .htaccess:</strong> Make sure there are no redirect rules interfering</li>";
echo "</ol>";
echo "</div>";

echo "<p><strong>⚠ Delete this file after debugging for security reasons.</strong></p>";
?>
