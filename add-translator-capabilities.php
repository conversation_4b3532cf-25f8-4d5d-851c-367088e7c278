<?php
/**
 * Add Team System Capabilities to Translator Role
 * Run this script once to give translators access to team system
 */

// Include WordPress
require_once('../../../wp-config.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    die('Access denied. You need administrator privileges to run this script.');
}

echo "<h2>Adding Team System Capabilities</h2>";

// Get the translator role
$translator_role = get_role('translator');

if ($translator_role) {
    echo "<h3>Adding capabilities to 'translator' role:</h3>";
    
    // Add team system capabilities to translator role
    $capabilities = array(
        'access_team_system' => 'Access team system pages',
        'create_teams' => 'Create new teams',
        'join_teams' => 'Join existing teams',
        'edit_posts' => 'Basic WordPress capability (if not already present)'
    );
    
    foreach ($capabilities as $cap => $description) {
        $translator_role->add_cap($cap);
        echo "<p style='color: green;'>✓ Added capability: <strong>$cap</strong> - $description</p>";
    }
    
    echo "<p style='color: green; font-weight: bold;'>✓ Translator role updated successfully!</p>";
} else {
    echo "<p style='color: red;'>✗ Translator role not found!</p>";
    echo "<p>Available roles:</p>";
    echo "<ul>";
    
    global $wp_roles;
    foreach ($wp_roles->roles as $role_name => $role_info) {
        echo "<li><strong>$role_name</strong>: " . $role_info['name'] . "</li>";
    }
    echo "</ul>";
}

// Also add to other roles that should have access
echo "<h3>Adding capabilities to other roles:</h3>";
$roles_with_access = array('administrator', 'editor', 'author');

foreach ($roles_with_access as $role_name) {
    $role = get_role($role_name);
    if ($role) {
        $role->add_cap('access_team_system');
        $role->add_cap('create_teams');
        $role->add_cap('join_teams');
        echo "<p style='color: green;'>✓ Updated role: <strong>$role_name</strong></p>";
    } else {
        echo "<p style='color: orange;'>⚠ Role not found: <strong>$role_name</strong></p>";
    }
}

// Test current user capabilities
echo "<h3>Current User Capability Test</h3>";
$current_user = wp_get_current_user();
echo "<p>Current User: <strong>" . $current_user->user_login . "</strong></p>";
echo "<p>Current User Roles: <strong>" . implode(', ', $current_user->roles) . "</strong></p>";

$test_capabilities = array(
    'manage_options',
    'access_team_system',
    'create_teams',
    'join_teams',
    'edit_posts',
    'translator'
);

echo "<h4>Capability Check:</h4>";
foreach ($test_capabilities as $cap) {
    $has_cap = current_user_can($cap);
    $status = $has_cap ? '✓' : '✗';
    $color = $has_cap ? 'green' : 'red';
    echo "<p style='color: $color;'>$status <strong>$cap</strong></p>";
}

// Create a test translator user if needed
echo "<h3>Test Translator User</h3>";

$test_username = 'test_translator';
$test_user = get_user_by('login', $test_username);

if (!$test_user) {
    echo "<form method='post' style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd;'>";
    echo "<h4>Create Test Translator User</h4>";
    echo "<p>This will create a test user with translator role to test team system access.</p>";
    echo "<label>Username:</label><br>";
    echo "<input type='text' name='test_username' value='$test_username' readonly><br><br>";
    echo "<label>Email:</label><br>";
    echo "<input type='email' name='test_email' value='<EMAIL>' required><br><br>";
    echo "<label>Password:</label><br>";
    echo "<input type='password' name='test_password' value='translator123' required><br><br>";
    echo "<input type='submit' name='create_test_user' value='Create Test User' class='button button-primary'>";
    echo "</form>";
    
    if (isset($_POST['create_test_user'])) {
        $user_data = array(
            'user_login' => sanitize_text_field($_POST['test_username']),
            'user_email' => sanitize_email($_POST['test_email']),
            'user_pass' => $_POST['test_password'],
            'role' => 'translator'
        );
        
        $user_id = wp_insert_user($user_data);
        
        if (!is_wp_error($user_id)) {
            echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
            echo "✓ Test translator user created successfully!<br>";
            echo "Username: " . $user_data['user_login'] . "<br>";
            echo "Password: " . $user_data['user_pass'] . "<br>";
            echo "You can now log in with this user to test team system access.";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
            echo "✗ Failed to create test user: " . $user_id->get_error_message();
            echo "</div>";
        }
    }
} else {
    echo "<p style='color: blue;'>Test translator user already exists: <strong>$test_username</strong></p>";
    echo "<p>You can log in with this user to test team system access.</p>";
}

// Test team system access
echo "<h3>Team System Access Test</h3>";

// Check if team system pages are accessible
$team_system_url = admin_url('admin.php?page=team-system');
$add_team_url = admin_url('admin.php?page=team-system-add');

echo "<h4>Test Links (try these after updating capabilities):</h4>";
echo "<ul>";
echo "<li><a href='$team_system_url' target='_blank'>Team System Main Page</a></li>";
echo "<li><a href='$add_team_url' target='_blank'>Add New Team Page</a></li>";
echo "</ul>";

// Instructions
echo "<h3>Testing Instructions</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #0073aa;'>";
echo "<h4>How to Test Translator Access:</h4>";
echo "<ol>";
echo "<li><strong>Log out</strong> from current admin account</li>";
echo "<li><strong>Log in</strong> as a translator user (or create one above)</li>";
echo "<li><strong>Check the admin menu</strong> - you should see 'الفرق' in the sidebar</li>";
echo "<li><strong>Click on 'الفرق'</strong> - you should be able to access the team system</li>";
echo "<li><strong>Try creating a team</strong> - translators should be able to create teams</li>";
echo "<li><strong>Check admin bar</strong> - 'الفرق' should appear in the top admin bar</li>";
echo "</ol>";
echo "</div>";

// Troubleshooting
echo "<h3>Troubleshooting</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>";
echo "<h4>If translators still can't access team system:</h4>";
echo "<ul>";
echo "<li>Make sure the translator role exists in your WordPress installation</li>";
echo "<li>Check that capabilities were added successfully (see capability check above)</li>";
echo "<li>Try logging out and back in to refresh user capabilities</li>";
echo "<li>Clear any caching plugins that might cache user capabilities</li>";
echo "<li>Check WordPress error logs for any permission errors</li>";
echo "</ul>";
echo "</div>";

// Clean up option
echo "<h3>Remove Test Data</h3>";
echo "<form method='post' style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>";
echo "<h4>Remove Test User</h4>";
echo "<p>This will remove the test translator user if you no longer need it.</p>";
echo "<input type='submit' name='remove_test_user' value='Remove Test User' class='button button-secondary' onclick='return confirm(\"Are you sure you want to remove the test user?\");'>";
echo "</form>";

if (isset($_POST['remove_test_user'])) {
    $test_user = get_user_by('login', $test_username);
    if ($test_user) {
        wp_delete_user($test_user->ID);
        echo "<div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
        echo "✓ Test user removed successfully";
        echo "</div>";
    }
}

echo "<p><strong>Note:</strong> Delete this file after setup for security reasons.</p>";
?>
