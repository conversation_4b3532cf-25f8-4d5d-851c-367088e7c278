# حل مشكلة "Unknown column 'user_id' in 'field list'"

## المشكلة
الخطأ يشير إلى أن عمود `user_id` غير موجود في جدول `team_invitations`، مما يعني أن الجدول لم يتم إنشاؤه بالشكل الصحيح.

## الحل السريع

### الخطوة 1: تشغيل سكريبت التحديث المحسن
1. افتح المتصفح واذهب إلى: `http://localhost/wp-content/plugins/team-system/update-invitations-table.php`
2. السكريبت الآن سيتحقق من وجود جميع الأعمدة المطلوبة
3. إذا كانت أعمدة مفقودة، سيحذف الجدول وينشئه من جديد
4. تأكد من ظهور رسالة "✓ All required columns exist" أو "✓ Created table"

### الخطوة 2: اختبار النظام الجديد
1. اذهب إلى صفحة تحرير فريق: `/wp-admin/admin.php?page=team-system&action=edit&team_id=1`
2. انقر على تبويب "إدارة الأعضاء"
3. في قسم "دعوة أعضاء جدد":
   - يمكنك الآن إدخال **اسم المستخدم** (مثل: admin) أو **البريد الإلكتروني**
   - اختر الدور المطلوب
   - انقر "إرسال دعوة"

## التحسينات الجديدة

### 1. دعم اسم المستخدم والبريد الإلكتروني
- **قبل**: البريد الإلكتروني فقط
- **بعد**: اسم المستخدم أو البريد الإلكتروني

```javascript
// أمثلة صحيحة:
"admin"                    // اسم المستخدم
"user123"                  // اسم المستخدم
"<EMAIL>"         // البريد الإلكتروني
```

### 2. تحسين سكريبت إنشاء الجدول
- فحص وجود جميع الأعمدة المطلوبة
- إعادة إنشاء الجدول إذا كانت أعمدة مفقودة
- رسائل تشخيص أوضح

### 3. معالجة أخطاء محسنة
- رسائل خطأ أكثر وضوحاً
- تسجيل الأخطاء في WordPress log
- تشخيص أفضل للمشاكل

## بنية الجدول الصحيحة

```sql
CREATE TABLE wp_team_invitations (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    team_id bigint(20) NOT NULL,
    email varchar(100) NOT NULL,
    user_id bigint(20) DEFAULT NULL,
    role varchar(50) NOT NULL,
    token varchar(100) NOT NULL,
    invited_by bigint(20) NOT NULL,
    created_at datetime NOT NULL,
    expires_at datetime NOT NULL,
    status varchar(20) DEFAULT 'pending',
    PRIMARY KEY (id),
    UNIQUE KEY token (token),
    KEY team_id (team_id),
    KEY user_id (user_id),
    KEY status (status),
    KEY expires_at (expires_at)
);
```

## الأعمدة المطلوبة
- `id`: معرف الدعوة
- `team_id`: معرف الفريق
- `email`: البريد الإلكتروني للمدعو
- `user_id`: معرف المستخدم في WordPress
- `role`: دور العضو في الفريق
- `token`: رمز فريد للدعوة
- `invited_by`: معرف الشخص الذي أرسل الدعوة
- `created_at`: تاريخ إنشاء الدعوة
- `expires_at`: تاريخ انتهاء صلاحية الدعوة
- `status`: حالة الدعوة (pending, accepted, declined, cancelled)

## اختبار النظام

### اختبار 1: دعوة بالبريد الإلكتروني
```
1. أدخل: <EMAIL>
2. اختر دور: عضو
3. انقر "إرسال دعوة"
4. يجب أن تظهر: "تم إرسال دعوة الانضمام للفريق بنجاح"
```

### اختبار 2: دعوة باسم المستخدم
```
1. أدخل: admin
2. اختر دور: مشرف
3. انقر "إرسال دعوة"
4. يجب أن تظهر: "تم إرسال دعوة الانضمام للفريق بنجاح"
```

### اختبار 3: مستخدم غير موجود
```
1. أدخل: nonexistentuser
2. انقر "إرسال دعوة"
3. يجب أن تظهر: "لم يتم العثور على مستخدم بهذا الاسم أو البريد الإلكتروني"
```

## التحقق من نجاح الحل

### 1. فحص الجدول في قاعدة البيانات
```sql
-- في phpMyAdmin
DESCRIBE wp_team_invitations;
-- يجب أن ترى جميع الأعمدة المذكورة أعلاه
```

### 2. فحص البيانات المُدرجة
```sql
SELECT * FROM wp_team_invitations ORDER BY created_at DESC;
-- يجب أن ترى الدعوات المُرسلة
```

### 3. فحص الدعوات في الواجهة
- اذهب إلى `/wp-admin/admin.php?page=team-system`
- يجب أن ترى بطاقات الدعوات للمستخدمين المدعوين

## استكشاف الأخطاء

### إذا استمر خطأ "Unknown column"
1. تأكد من تشغيل `update-invitations-table.php` بنجاح
2. تحقق من أن جميع الأعمدة موجودة في الجدول
3. جرب حذف الجدول وإعادة إنشائه:
   ```sql
   DROP TABLE wp_team_invitations;
   ```
   ثم شغل السكريبت مرة أخرى

### إذا لم يتم العثور على المستخدم
1. تأكد من أن المستخدم موجود في WordPress
2. تأكد من كتابة اسم المستخدم أو البريد بشكل صحيح
3. تحقق من أن المستخدم لم يتم حذفه

### إذا لم تظهر الدعوات
1. تحقق من أن المستخدم المدعو يسجل دخول للموقع
2. تأكد من أن الدعوة لم تنته صلاحيتها (7 أيام)
3. تحقق من جدول `team_invitations` في قاعدة البيانات

## الملفات المُحدثة
- `update-invitations-table.php`: تحسين فحص الأعمدة
- `admin/class-team-system-admin.php`: دعم اسم المستخدم
- `admin/partials/team-system-edit-team.php`: واجهة محسنة
- `INVITATION-COLUMN-FIX.md`: دليل الحل

## ملاحظات أمنية
- احذف ملفات التشخيص بعد الانتهاء
- تأكد من صحة صلاحيات قاعدة البيانات
- لا تشارك معلومات قاعدة البيانات
