# حل مشكلة وصول المترجمين لنظام الفرق

## المشكلة
المستخدمون الذين لديهم دور "translator" لا يمكنهم الوصول إلى صفحة `wp-admin/admin.php?page=team-system` لأن النظام يتطلب صلاحيات إدارية أعلى.

## الحل السريع

### الخطوة 1: تشغيل سكريبت إضافة الصلاحيات
```
http://localhost/wp-content/plugins/team-system/add-translator-capabilities.php
```

هذا السكريبت سيقوم بـ:
- إضافة صلاحيات نظام الفرق لدور "translator"
- إضافة نفس الصلاحيات للأدوار الأخرى (administrator, editor, author)
- إنشاء مستخدم تجريبي للاختبار
- اختبار الوصول للنظام

### الخطوة 2: اختبار الوصول
1. سجل خروج من حساب الإدارة
2. سجل دخول بحساب مترجم
3. تحقق من ظهور "الفرق" في القائمة الجانبية
4. انقر على "الفرق" للوصول للنظام

## التحديثات التقنية

### 1. إضافة دالة الصلاحيات
```php
public function add_translator_capabilities() {
    // Get the translator role
    $translator_role = get_role('translator');
    
    if ($translator_role) {
        // Add team system capabilities
        $translator_role->add_cap('access_team_system');
        $translator_role->add_cap('create_teams');
        $translator_role->add_cap('join_teams');
    }
}
```

### 2. تحديث منطق الصلاحيات المطلوبة
```php
private function get_required_capability() {
    if (current_user_can('manage_options')) {
        return 'manage_options';  // Administrators
    } elseif (current_user_can('access_team_system')) {
        return 'access_team_system';  // Users with team system access
    } else {
        return 'edit_posts';  // Fallback
    }
}
```

### 3. تحديث قائمة الإدارة
```php
add_menu_page(
    'إدارة الفرق',
    'الفرق',
    $required_cap,  // Dynamic capability instead of 'manage_options'
    'team-system',
    array($this, 'display_plugin_admin_page'),
    'dashicons-groups',
    26
);
```

## الصلاحيات الجديدة

### للمترجمين (translator):
- `access_team_system`: الوصول لصفحات نظام الفرق
- `create_teams`: إنشاء فرق جديدة
- `join_teams`: الانضمام للفرق الموجودة
- `edit_posts`: الصلاحية الأساسية (إذا لم تكن موجودة)

### للأدوار الأخرى:
- **Administrator**: جميع الصلاحيات + إدارة كاملة
- **Editor**: جميع صلاحيات الفرق + تحرير المحتوى
- **Author**: إنشاء والانضمام للفرق

## اختبار النظام

### اختبار 1: وصول المترجم
```
1. سجل دخول كمترجم
2. تحقق من ظهور "الفرق" في القائمة الجانبية
3. انقر على "الفرق"
4. يجب أن تصل لصفحة إدارة الفرق بنجاح
```

### اختبار 2: إنشاء فريق
```
1. من صفحة الفرق، انقر "إضافة فريق جديد"
2. املأ بيانات الفريق
3. احفظ الفريق
4. يجب أن يتم الإنشاء بنجاح
```

### اختبار 3: إشعار شريط الإدارة
```
1. أنشئ دعوة لمترجم آخر
2. سجل دخول بحساب المترجم المدعو
3. تحقق من ظهور إشعار "الفرق (1)" في شريط الإدارة العلوي
4. انقر على الإشعار للوصول للدعوات
```

## استكشاف الأخطاء

### إذا لم يظهر "الفرق" في القائمة:
1. **تحقق من وجود دور translator**:
   ```php
   $translator_role = get_role('translator');
   var_dump($translator_role); // Should not be null
   ```

2. **تحقق من الصلاحيات**:
   ```php
   $user = wp_get_current_user();
   var_dump($user->allcaps); // Should contain 'access_team_system'
   ```

3. **تشغيل سكريبت الصلاحيات مرة أخرى**

### إذا ظهر خطأ "Access Denied":
1. **تحقق من الصلاحية المطلوبة في الكود**
2. **امسح الكاش إذا كنت تستخدم plugins للتخزين المؤقت**
3. **سجل خروج ودخول مرة أخرى**

### إذا لم تعمل الدعوات:
1. **تحقق من جدول team_invitations**
2. **تأكد من تشغيل update-invitations-table.php**
3. **فحص WordPress error logs**

## الأمان والصلاحيات

### مستويات الوصول:
1. **Administrator**: وصول كامل + إدارة النظام
2. **Editor**: وصول كامل للفرق + تحرير المحتوى
3. **Author**: إنشاء فرق + الانضمام
4. **Translator**: إنشاء فرق + الانضمام + ترجمة
5. **Contributor**: الانضمام للفرق فقط (إذا تم دعوته)

### الحماية:
- فحص الصلاحيات في كل صفحة
- التحقق من nonce في طلبات AJAX
- تنظيف البيانات المُدخلة
- فحص ملكية الفريق قبل التعديل

## الملفات المُحدثة

### 1. admin/class-team-system-admin.php
- إضافة `add_translator_capabilities()`
- تحديث `get_required_capability()`
- تحديث `add_plugin_admin_menu()`

### 2. add-translator-capabilities.php
- سكريبت إضافة الصلاحيات
- اختبار الوصول
- إنشاء مستخدم تجريبي

### 3. TRANSLATOR-ACCESS-FIX.md
- دليل شامل لحل المشكلة
- خطوات الاختبار
- استكشاف الأخطاء

## خطوات ما بعد الإعداد

### 1. اختبار شامل:
- اختبر جميع الأدوار (admin, editor, author, translator)
- تأكد من عمل الدعوات
- اختبر إنشاء وتحرير الفرق

### 2. تنظيف:
- احذف ملفات الاختبار والإعداد
- احذف المستخدمين التجريبيين
- نظف البيانات التجريبية

### 3. توثيق:
- وثق الصلاحيات الجديدة للمستخدمين
- أنشئ دليل استخدام للمترجمين
- حدث سياسات الأمان

## الميزات الجديدة للمترجمين

### واجهة مخصصة:
- قائمة "الفرق" في الشريط الجانبي
- إشعارات الدعوات في شريط الإدارة العلوي
- صفحة إدارة فرق مبسطة

### إمكانيات متقدمة:
- إنشاء فرق ترجمة جديدة
- دعوة مترجمين آخرين
- إدارة مشاريع الترجمة
- تتبع تقدم العمل

### تكامل مع النظام:
- ربط الفرق بالفصول المترجمة
- عرض إحصائيات الفريق
- نظام إشعارات متقدم

## الخطوات التالية المقترحة
1. إضافة لوحة تحكم مخصصة للمترجمين
2. نظام تقييم أداء الفرق
3. تقارير إحصائية للترجمة
4. تكامل مع أدوات الترجمة الخارجية
5. نظام مكافآت ونقاط للمترجمين
