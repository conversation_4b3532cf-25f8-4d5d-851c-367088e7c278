<div class="wrap">
    <h1 class="wp-heading-inline">إدارة الفرق</h1>
    <a href="<?php echo admin_url('admin.php?page=team-system-add'); ?>" class="page-title-action">إضافة فريق جديد</a>
    <hr class="wp-header-end">

    <?php
    // Show team invitations for current user
    global $wpdb;
    $current_user_id = get_current_user_id();
    $pending_invitations = $wpdb->get_results($wpdb->prepare(
        "SELECT i.*, t.name as team_name, t.slug as team_slug, u.display_name as invited_by_name
         FROM {$wpdb->prefix}team_invitations i
         JOIN {$wpdb->prefix}teams t ON i.team_id = t.id
         JOIN {$wpdb->users} u ON i.invited_by = u.ID
         WHERE i.user_id = %d AND i.status = 'pending' AND i.expires_at > NOW()
         ORDER BY i.created_at DESC",
        $current_user_id
    ));

    if (!empty($pending_invitations)): ?>
        <div class="team-invitations-section">
            <h2>دعوات الانضمام للفرق</h2>
            <div class="invitations-grid">
                <?php foreach ($pending_invitations as $invitation): ?>
                    <div class="invitation-card" data-invitation-id="<?php echo $invitation->id; ?>">
                        <div class="invitation-header">
                            <h3>دعوة للانضمام إلى فريق</h3>
                            <span class="invitation-date"><?php echo date_i18n('j F Y', strtotime($invitation->created_at)); ?></span>
                        </div>

                        <div class="invitation-body">
                            <div class="team-info">
                                <h4 class="team-name"><?php echo esc_html($invitation->team_name); ?></h4>
                                <p class="invitation-text">
                                    دعاك <strong><?php echo esc_html($invitation->invited_by_name); ?></strong>
                                    للانضمام إلى الفريق بدور <strong><?php echo esc_html($this->get_role_name($invitation->role)); ?></strong>
                                </p>
                            </div>

                            <div class="invitation-expires">
                                <small>تنتهي صلاحية الدعوة في: <?php echo date_i18n('j F Y', strtotime($invitation->expires_at)); ?></small>
                            </div>
                        </div>

                        <div class="invitation-actions">
                            <button type="button" class="button button-primary accept-invitation"
                                    data-invitation-id="<?php echo $invitation->id; ?>">
                                قبول الدعوة
                            </button>
                            <button type="button" class="button decline-invitation"
                                    data-invitation-id="<?php echo $invitation->id; ?>">
                                رفض الدعوة
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Quick Team Creation Section -->
    <div class="quick-team-creation">
        <h2>إنشاء فريق جديد</h2>
        <p>يمكنك إنشاء فريق جديد بسرعة من هنا، أو الذهاب إلى صفحة الإنشاء المفصلة.</p>
        <div class="quick-create-buttons">
            <a href="<?php echo admin_url('admin.php?page=team-system-add'); ?>" class="button button-primary button-large">
                <span class="dashicons dashicons-plus-alt"></span>
                إنشاء فريق جديد
            </a>
            <a href="<?php echo admin_url('admin.php?page=team-system'); ?>" class="button button-secondary">
                <span class="dashicons dashicons-list-view"></span>
                عرض جميع الفرق
            </a>
        </div>
    </div>
    
    <div class="team-list">
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>الشعار</th>
                    <th>اسم الفريق</th>
                    <th>الأعضاء</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php
                global $wpdb;
                $teams = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}teams ORDER BY created_at DESC");
                
                foreach ($teams as $team) {
                    $member_count = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM {$wpdb->prefix}team_members WHERE team_id = %d",
                        $team->id
                    ));
                    ?>
                    <tr>
                        <td>
                            <?php if ($team->logo_url): ?>
                                <img src="<?php echo esc_url($team->logo_url); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;">
                            <?php else: ?>
                                <div style="width: 50px; height: 50px; background: #f0f0f1; display: flex; align-items: center; justify-content: center; border-radius: 4px;">
                                    <span class="dashicons dashicons-groups" style="font-size: 24px; width: 24px; height: 24px; color: #8c8f94;"></span>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?php echo esc_html($team->name); ?></strong>
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="<?php echo admin_url('admin.php?page=team-system&action=edit&team_id=' . $team->id); ?>">تعديل</a> |
                                </span>
                                <span class="view">
                                    <a href="<?php echo home_url('/teams/' . $team->slug); ?>" target="_blank">عرض</a>
                                </span>
                            </div>
                        </td>
                        <td><?php echo intval($member_count); ?> عضو</td>
                        <td><?php echo date_i18n('j F Y', strtotime($team->created_at)); ?></td>
                        <td>
                            <a href="<?php echo admin_url('admin.php?page=team-system&action=edit&team_id=' . $team->id); ?>" class="button button-primary">تعديل</a>
                            <a href="#" class="button delete-team" data-team-id="<?php echo $team->id; ?>">حذف</a>
                        </td>
                    </tr>
                    <?php
                }
                
                if (empty($teams)): ?>
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px;">
                            لا توجد فرق حتى الآن. <a href="<?php echo admin_url('admin.php?page=team-system-add'); ?>">أنشئ فريقك الأول</a>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<script>
// Define AJAX variables if not already defined
if (typeof ajaxurl === 'undefined') {
    var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
}

// Define team system AJAX object if not already defined
if (typeof teamSystemAjax === 'undefined') {
    var teamSystemAjax = {
        nonce: '<?php echo wp_create_nonce('team_system_nonce'); ?>',
        ajaxurl: ajaxurl
    };
}

jQuery(document).ready(function($) {
    // Handle team deletion
    $('.delete-team').on('click', function(e) {
        e.preventDefault();
        
        if (!confirm('هل أنت متأكد من حذف هذا الفريق؟ لا يمكن التراجع عن هذا الإجراء.')) {
            return;
        }
        
        var $button = $(this);
        var teamId = $button.data('team-id');
        
        $.post(ajaxurl, {
            action: 'team_system_delete_team',
            team_id: teamId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $button.closest('tr').fadeOut(300, function() {
                    $(this).remove();
                });
            } else {
                alert(response.data.message || 'حدث خطأ أثناء حذف الفريق');
            }
        }).fail(function() {
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Accept invitation
    $('.accept-invitation').on('click', function() {
        var $card = $(this).closest('.invitation-card');
        var invitationId = $(this).data('invitation-id');

        $card.addClass('loading');

        $.post(ajaxurl, {
            action: 'team_system_accept_invitation',
            invitation_id: invitationId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $card.fadeOut(300, function() {
                    $(this).remove();
                    // Show success message
                    $('.team-invitations-section').prepend(
                        '<div class="invitation-message success">' +
                        (response.data.message || 'تم قبول الدعوة بنجاح!') +
                        '</div>'
                    );

                    // Check if no more invitations
                    if ($('.invitation-card').length === 0) {
                        $('.team-invitations-section').fadeOut(500);
                    }
                });
            } else {
                $card.removeClass('loading');
                alert(response.data.message || 'حدث خطأ أثناء قبول الدعوة');
            }
        }).fail(function() {
            $card.removeClass('loading');
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });

    // Decline invitation
    $('.decline-invitation').on('click', function() {
        if (!confirm('هل أنت متأكد من رفض هذه الدعوة؟')) {
            return;
        }

        var $card = $(this).closest('.invitation-card');
        var invitationId = $(this).data('invitation-id');

        $card.addClass('loading');

        $.post(ajaxurl, {
            action: 'team_system_decline_invitation',
            invitation_id: invitationId,
            nonce: teamSystemAjax.nonce
        }, function(response) {
            if (response.success) {
                $card.fadeOut(300, function() {
                    $(this).remove();

                    // Check if no more invitations
                    if ($('.invitation-card').length === 0) {
                        $('.team-invitations-section').fadeOut(500);
                    }
                });
            } else {
                $card.removeClass('loading');
                alert(response.data.message || 'حدث خطأ أثناء رفض الدعوة');
            }
        }).fail(function() {
            $card.removeClass('loading');
            alert('حدث خطأ في الاتصال بالخادم');
        });
    });
});
</script>

<style>
/* Team Invitations Styling */
.team-invitations-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.team-invitations-section h2 {
    margin-top: 0;
    color: #1d2327;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.invitations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.invitation-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.invitation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.invitation-header {
    background: rgba(255,255,255,0.1);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.invitation-header h3 {
    color: white;
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.invitation-date {
    color: rgba(255,255,255,0.8);
    font-size: 12px;
}

.invitation-body {
    padding: 20px;
    color: white;
}

.team-name {
    color: white;
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 10px 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.invitation-text {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    margin: 0 0 15px 0;
}

.invitation-expires {
    color: rgba(255,255,255,0.7);
    font-size: 12px;
}

.invitation-actions {
    padding: 15px 20px;
    background: rgba(255,255,255,0.1);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.invitation-actions .button {
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.invitation-actions .button-primary {
    background: #28a745;
    color: white;
}

.invitation-actions .button-primary:hover {
    background: #218838;
    transform: translateY(-1px);
}

.invitation-actions .decline-invitation {
    background: rgba(255,255,255,0.2);
    color: white;
}

.invitation-actions .decline-invitation:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.invitation-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.invitation-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
    font-weight: 500;
}

.invitation-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.invitation-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Quick Team Creation Styling */
.quick-team-creation {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 8px;
    padding: 25px;
    margin-top: 30px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.quick-team-creation h2 {
    margin-top: 0;
    color: #1d2327;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.quick-team-creation p {
    color: #646970;
    margin-bottom: 20px;
    font-size: 14px;
}

.quick-create-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.quick-create-buttons .button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.quick-create-buttons .button-primary {
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    border: none;
    color: white;
    box-shadow: 0 2px 8px rgba(0,115,170,0.3);
}

.quick-create-buttons .button-primary:hover {
    background: linear-gradient(135deg, #005177 0%, #003d5c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,115,170,0.4);
}

.quick-create-buttons .button-secondary {
    background: #f6f7f7;
    border: 1px solid #dcdcde;
    color: #50575e;
}

.quick-create-buttons .button-secondary:hover {
    background: #f0f0f1;
    border-color: #8c8f94;
    color: #1d2327;
    transform: translateY(-1px);
}

.quick-create-buttons .dashicons {
    font-size: 16px;
}

/* Admin Bar Notification Styling */
#wp-admin-bar-team-invitations .ab-item,
#wp-admin-bar-team-system .ab-item {
    color: #a7aaad !important;
}

#wp-admin-bar-team-invitations:hover .ab-item,
#wp-admin-bar-team-system:hover .ab-item {
    color: #00b9eb !important;
}

#wp-admin-bar-team-invitations .awaiting-mod {
    background: #d63638;
    color: #fff;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 600;
    margin-left: 5px;
    min-width: 18px;
    text-align: center;
    display: inline-block;
}

#wp-admin-bar-team-invitations .ab-icon:before {
    content: "\f307";
    top: 2px;
}

#wp-admin-bar-team-system .ab-icon:before {
    content: "\f307";
    top: 2px;
}
</style>